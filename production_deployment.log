2025-07-13 14:00:44,537 - __main__ - INFO - 🚀 Dr. Apex Production Deployment Starting...
2025-07-13 14:00:44,537 - __main__ - INFO - Loading production environment...
2025-07-13 14:00:44,537 - __main__ - INFO - ✅ Environment loaded successfully
2025-07-13 14:00:44,537 - ProductionQuantitativeSystem - INFO - Initializing production quantitative system...
2025-07-13 14:00:44,919 - src.core.system - INFO - {"event": "Found 5 valid API keys", "logger": "src.core.system", "level": "info", "timestamp": "2025-07-13T19:00:44.919192Z"}
2025-07-13 14:00:44,919 - src.core.system - INFO - {"event": "Core system initialized successfully", "logger": "src.core.system", "level": "info", "timestamp": "2025-07-13T19:00:44.919319Z"}
2025-07-13 14:00:44,998 - src.data.sources - INFO - {"event": "Alpha Vantage source initialized", "logger": "src.data.sources", "level": "info", "timestamp": "2025-07-13T19:00:44.998792Z"}
2025-07-13 14:00:44,998 - src.data.sources - INFO - {"event": "Finnhub source initialized", "logger": "src.data.sources", "level": "info", "timestamp": "2025-07-13T19:00:44.998935Z"}
2025-07-13 14:00:44,999 - src.data.sources - INFO - {"event": "FRED source initialized", "logger": "src.data.sources", "level": "info", "timestamp": "2025-07-13T19:00:44.998999Z"}
2025-07-13 14:00:44,999 - src.data.sources - INFO - {"available_sources": ["alpha_vantage", "finnhub", "fred"], "event": "Data sources module initialized", "logger": "src.data.sources", "level": "info", "timestamp": "2025-07-13T19:00:44.999040Z"}
2025-07-13 14:00:46,656 - src.agents.base - INFO - {"agent": "quantitative", "config": {"lookback_period": 252, "min_observations": 50, "confidence_level": 0.95, "risk_free_rate": 0.03, "factor_models": ["fama_french", "momentum"]}, "event": "Initializing agent", "logger": "src.agents.base", "level": "info", "timestamp": "2025-07-13T19:00:46.655142Z"}
2025-07-13 14:00:46,656 - src.data.sources - INFO - {"event": "Alpha Vantage source initialized", "logger": "src.data.sources", "level": "info", "timestamp": "2025-07-13T19:00:46.656223Z"}
2025-07-13 14:00:46,656 - src.data.sources - INFO - {"event": "Finnhub source initialized", "logger": "src.data.sources", "level": "info", "timestamp": "2025-07-13T19:00:46.656324Z"}
2025-07-13 14:00:46,656 - src.data.sources - INFO - {"event": "FRED source initialized", "logger": "src.data.sources", "level": "info", "timestamp": "2025-07-13T19:00:46.656386Z"}
2025-07-13 14:00:46,656 - src.agents.base - INFO - {"agent": "quantitative", "source_name": "alpha_vantage", "total_sources": 1, "event": "Data source added", "logger": "src.agents.base", "level": "info", "timestamp": "2025-07-13T19:00:46.656427Z"}
2025-07-13 14:00:46,656 - src.agents.base - INFO - {"agent": "quantitative", "event": "Added data source: alpha_vantage", "logger": "src.agents.base", "level": "info", "timestamp": "2025-07-13T19:00:46.656463Z"}
2025-07-13 14:00:46,656 - src.agents.base - INFO - {"agent": "quantitative", "source_name": "finnhub", "total_sources": 2, "event": "Data source added", "logger": "src.agents.base", "level": "info", "timestamp": "2025-07-13T19:00:46.656494Z"}
2025-07-13 14:00:46,656 - src.agents.base - INFO - {"agent": "quantitative", "event": "Added data source: finnhub", "logger": "src.agents.base", "level": "info", "timestamp": "2025-07-13T19:00:46.656525Z"}
2025-07-13 14:00:46,656 - src.agents.base - INFO - {"agent": "quantitative", "event": "Quantitative agent resources initialized", "logger": "src.agents.base", "level": "info", "timestamp": "2025-07-13T19:00:46.656555Z"}
2025-07-13 14:00:46,656 - src.agents.base - INFO - {"agent": "quantitative", "event": "Agent initialized successfully", "logger": "src.agents.base", "level": "info", "timestamp": "2025-07-13T19:00:46.656591Z"}
2025-07-13 14:00:46,656 - ProductionQuantitativeSystem - INFO - ✅ Quantitative agent initialized
2025-07-13 14:00:47,011 - src.analysis.sentiment - INFO - {"available_analyzers": ["news", "sec_filings", "earnings_calls"], "event": "Advanced sentiment analysis module initialized", "logger": "src.analysis.sentiment", "level": "info", "timestamp": "2025-07-13T19:00:47.011875Z"}
2025-07-13 14:00:47,012 - ProductionQuantitativeSystem - INFO - ✅ Stock forecasting engine initialized
2025-07-13 14:00:47,012 - ProductionQuantitativeSystem - INFO - ✅ Master orchestrator components initialized
2025-07-13 14:00:47,012 - ProductionQuantitativeSystem - INFO - ✅ Production system initialized successfully
2025-07-13 14:00:47,012 - __main__ - INFO - 🧪 Testing with sample portfolio: ['AAPL', 'MSFT', 'GOOGL']
2025-07-13 14:00:47,012 - ProductionQuantitativeSystem - INFO - Starting portfolio analysis for ['AAPL', 'MSFT', 'GOOGL']
2025-07-13 14:00:47,012 - ProductionQuantitativeSystem - INFO - Running quantitative analysis...
2025-07-13 14:00:47,012 - src.agents.base - INFO - {"agent": "quantitative", "symbols": ["AAPL", "MSFT", "GOOGL"], "timeframe": "1d", "event": "Starting analysis", "logger": "src.agents.base", "level": "info", "timestamp": "2025-07-13T19:00:47.012795Z"}
2025-07-13 14:00:47,021 - src.agents.base - ERROR - {"agent": "quantitative", "error": "SVD did not converge", "event": "Risk model building failed", "logger": "src.agents.base", "level": "error", "timestamp": "2025-07-13T19:00:47.021508Z"}
2025-07-13 14:00:47,396 - src.agents.base - INFO - {"agent": "quantitative", "duration": 0.383762, "confidence": 0.6096753587200873, "data_quality": 1.0, "event": "Analysis completed successfully", "logger": "src.agents.base", "level": "info", "timestamp": "2025-07-13T19:00:47.396564Z"}
2025-07-13 14:00:47,396 - ProductionQuantitativeSystem - INFO - ✅ Quantitative analysis complete (confidence: 0.610)
2025-07-13 14:00:47,396 - ProductionQuantitativeSystem - INFO - Generating stock forecasts...
2025-07-13 14:00:47,858 - ProductionQuantitativeSystem - INFO - ✅ AAPL forecast: -0.020 return
2025-07-13 14:00:48,044 - ProductionQuantitativeSystem - INFO - ✅ MSFT forecast: -0.027 return
2025-07-13 14:00:48,134 - ProductionQuantitativeSystem - INFO - ✅ GOOGL forecast: -0.023 return
2025-07-13 14:00:48,134 - ProductionQuantitativeSystem - INFO - Assessing black swan risks...
2025-07-13 14:00:48,134 - ProductionQuantitativeSystem - INFO - ✅ Risk assessment complete (0 risks identified)
2025-07-13 14:00:48,134 - ProductionQuantitativeSystem - INFO - ✅ Portfolio analysis complete
2025-07-13 14:00:48,135 - __main__ - INFO - ✅ Analysis results saved to production_analysis_20250713_140048.json
2025-07-13 14:00:48,135 - __main__ - INFO - 📊 Production Analysis Summary:
2025-07-13 14:00:48,135 - __main__ - INFO -    Overall Confidence: 0.610
2025-07-13 14:00:48,135 - __main__ - INFO -    Components Used: 3
2025-07-13 14:00:48,135 - __main__ - INFO -    Recommendations: 3
2025-07-13 14:00:48,135 - __main__ - INFO -    Risks Identified: 1
2025-07-13 14:00:48,135 - __main__ - INFO - 🏥 System Health: OPERATIONAL
2025-07-13 14:00:48,135 - __main__ - INFO - ✅ Production deployment test successful
