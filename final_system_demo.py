#!/usr/bin/env python3
"""
Final Demo - Complete Stock Forecasting System
Demonstrates the full functionality of the completed system
"""

import asyncio
import sys
import os
import json
from datetime import datetime

# Add the project root to Python path
sys.path.append('/Users/<USER>/crypto')

from src.orchestration.master_orchestrator import MasterOrchestrator

async def run_complete_demo():
    """Run comprehensive demo of the stock forecasting system"""
    print("🚀 COMPLETE STOCK FORECASTING SYSTEM DEMO")
    print("=" * 55)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Initialize the orchestrator
    print("📊 Initializing Master Orchestrator...")
    orchestrator = MasterOrchestrator()
    
    # Demo portfolio
    symbols = ["AAPL", "MSFT", "GOOGL", "TSLA"]
    print(f"📈 Portfolio symbols: {', '.join(symbols)}")
    print()
    
    try:
        # Run complete analysis
        print("🔍 Running comprehensive stock forecast analysis...")
        print("   - Fetching market data")
        print("   - Applying noise filtering")
        print("   - Generating technical forecasts")
        print("   - Integrating agent insights")
        print("   - Calculating portfolio metrics")
        print()
        
        result = await orchestrator.analyze_stock_forecast(
            symbols, 
            include_noise_analysis=True
        )
        
        # Display results
        print("✅ ANALYSIS COMPLETE!")
        print("=" * 55)
        
        # Overall summary
        print(f"🕒 Analysis timestamp: {result['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📊 Symbols processed: {len(result['symbols'])}")
        
        # System health
        health = result.get('system_health', {})
        print(f"⚡ System status: {health.get('status', 'unknown').upper()}")
        print(f"🤖 Active agents: {health.get('agents_active', 0)}")
        print()
        
        # Portfolio insights
        portfolio = result.get('portfolio_analysis', {})
        if portfolio:
            print("💼 PORTFOLIO ANALYSIS")
            print("-" * 25)
            print(f"📈 Sentiment: {portfolio.get('portfolio_sentiment', 'UNKNOWN')}")
            print(f"🎯 Average confidence: {portfolio.get('average_confidence', 0):.1%}")
            print(f"📊 Positive signals: {portfolio.get('positive_signals', 0)}")
            print(f"📉 Negative signals: {portfolio.get('negative_signals', 0)}")
            print(f"💡 {portfolio.get('recommendation', 'No recommendation available')}")
            print()
        
        # Individual stock forecasts
        forecasts = result.get('individual_forecasts', {})
        if forecasts:
            print("📈 INDIVIDUAL STOCK FORECASTS")
            print("-" * 35)
            
            for symbol, stock_data in forecasts.items():
                forecast = stock_data.get('forecast', {})
                consensus = forecast.get('consensus_forecast', {})
                
                expected_return = consensus.get('expected_return', 0)
                confidence = consensus.get('confidence', 0.5)
                quality = forecast.get('forecast_quality', 0.5)
                
                # Status indicator
                if expected_return > 0.02:
                    status = "🟢 BULLISH"
                elif expected_return < -0.02:
                    status = "🔴 BEARISH"  
                else:
                    status = "🟡 NEUTRAL"
                
                print(f"{symbol:6} | {status:12} | Return: {expected_return:+6.1%} | Confidence: {confidence:5.1%} | Quality: {quality:5.1%}")
                
                # Show timeframe breakdown
                timeframes = forecast.get('forecasts_by_timeframe', {})
                if timeframes:
                    for timeframe, tf_forecast in timeframes.items():
                        tf_return = tf_forecast.get('expected_return', 0)
                        tf_confidence = tf_forecast.get('confidence', 0.5)
                        print(f"       │ {timeframe:>3}: {tf_return:+6.1%} return ({tf_confidence:4.1%} confidence)")
                print()
        
        # Risk assessment
        risks = result.get('portfolio_risks', {})
        if risks:
            print("⚠️  RISK ASSESSMENT")
            print("-" * 20)
            print(f"🎚️  Risk level: {risks.get('risk_level', 'UNKNOWN')}")
            print(f"📊 Risk count: {risks.get('risk_count', 0)}")
            print(f"⚠️  Low confidence stocks: {risks.get('low_confidence_stocks', 0)}")
            
            risk_list = risks.get('risks', [])
            if risk_list:
                print("\n🔍 Specific risks identified:")
                for i, risk in enumerate(risk_list[:5], 1):  # Show top 5 risks
                    print(f"   {i}. {risk}")
            print()
        
        # Data quality summary
        noise_analysis = result.get('noise_analysis', {})
        if noise_analysis:
            print("🔬 DATA QUALITY SUMMARY")
            print("-" * 25)
            
            total_quality = 0
            quality_count = 0
            
            for symbol, noise_data in noise_analysis.items():
                quality = noise_data.get('data_quality', 0.8)
                noise_score = noise_data.get('noise_score', 0.2)
                total_quality += quality
                quality_count += 1
                
                quality_icon = "🟢" if quality > 0.8 else "🟡" if quality > 0.6 else "🔴"
                print(f"{symbol:6} | {quality_icon} Quality: {quality:5.1%} | Noise: {noise_score:5.1%}")
            
            avg_quality = total_quality / quality_count if quality_count > 0 else 0.8
            print(f"\n📊 Average data quality: {avg_quality:.1%}")
            print()
        
        # Confidence distribution
        confidence_summary = result.get('confidence_summary', {})
        if confidence_summary:
            print("🎯 CONFIDENCE DISTRIBUTION")
            print("-" * 28)
            
            dist = confidence_summary.get('confidence_distribution', {})
            total = confidence_summary.get('total_stocks', 0)
            
            if dist and total > 0:
                high = dist.get('high', 0)
                medium = dist.get('medium', 0)
                low = dist.get('low', 0)
                
                print(f"🟢 High confidence (>70%):   {high:2d}/{total} ({high/total*100:4.1f}%)")
                print(f"🟡 Medium confidence (50-70%): {medium:2d}/{total} ({medium/total*100:4.1f}%)")
                print(f"🔴 Low confidence (<50%):    {low:2d}/{total} ({low/total*100:4.1f}%)")
            print()
        
        # Performance summary
        performance = orchestrator.get_performance_summary()
        print("📊 SYSTEM PERFORMANCE")
        print("-" * 22)
        print(f"📈 Total forecasts generated: {performance.get('total_forecasts', 0)}")
        print(f"🎯 Performance trend: {performance.get('performance_trend', 'unknown').upper()}")
        print(f"⚡ System status: {performance.get('status', 'unknown').upper()}")
        print()
        
        print("✨ DEMO COMPLETED SUCCESSFULLY!")
        print("📋 The system is ready for production use.")
        print("🔄 Run this script anytime to get fresh market analysis.")
        
        return True
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def quick_single_stock_demo():
    """Quick demo with a single stock"""
    print("\n" + "="*55)
    print("🎯 QUICK SINGLE STOCK DEMO")
    print("="*55)
    
    orchestrator = MasterOrchestrator()
    
    symbol = "AAPL"
    print(f"📊 Analyzing {symbol}...")
    
    try:
        result = await orchestrator.analyze_stock_forecast([symbol])
        
        forecast_data = result['individual_forecasts'][symbol]['forecast']
        consensus = forecast_data['consensus_forecast']
        
        print(f"✅ {symbol} Analysis Complete:")
        print(f"   📈 Expected return: {consensus['expected_return']:+.2%}")
        print(f"   🎯 Confidence: {consensus['confidence']:.1%}")
        print(f"   ⭐ Signal strength: {consensus['signal_strength']:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Quick demo failed: {e}")
        return False

async def main():
    """Main demo runner"""
    print("Choose demo mode:")
    print("1. Complete Portfolio Demo (4 stocks)")
    print("2. Quick Single Stock Demo")
    print("3. Both")
    
    choice = input("\nEnter choice (1/2/3) [default: 3]: ").strip() or "3"
    
    success = True
    
    if choice in ["1", "3"]:
        success &= await run_complete_demo()
    
    if choice in ["2", "3"]:
        success &= await quick_single_stock_demo()
    
    print("\n" + "="*55)
    if success:
        print("🎉 ALL DEMOS COMPLETED SUCCESSFULLY!")
        print("💡 The stock forecasting system is fully operational.")
    else:
        print("⚠️  Some demos had issues. Check the logs above.")
    print("="*55)

if __name__ == "__main__":
    # Check dependencies
    try:
        import yfinance
        import pandas as pd
        import numpy as np
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("📦 Please run: pip install yfinance pandas numpy scipy")
        exit(1)
    
    asyncio.run(main())
