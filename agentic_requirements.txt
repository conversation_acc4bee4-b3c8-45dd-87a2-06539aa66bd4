# Hedge Fund Agentic System Requirements
# LangGraph + Multi-Agent Framework Dependencies

# Core LangGraph Framework
langgraph>=0.2.0
langchain>=0.3.0
langchain-openai>=0.2.0
langchain-core>=0.3.0
langchain-community>=0.3.0

# Enhanced AI Capabilities
openai>=1.50.0
anthropic>=0.34.0
google-generativeai>=0.8.0

# Multi-Agent Orchestration
crewai>=0.70.0  # Alternative framework for comparison
autogen-agentchat>=0.4.0

# Advanced Data Processing
pandas>=2.2.0
numpy>=1.26.0
scipy>=1.14.0
scikit-learn>=1.5.0

# Financial Data APIs (existing)
yfinance>=0.2.40
alpha-vantage>=2.3.1
finnhub-python>=2.4.20
fredapi>=0.5.2
polygon-api-client>=1.14.0

# Alternative Data Sources
requests>=2.32.0
beautifulsoup4>=4.12.0
selenium>=4.25.0
scrapy>=2.11.0

# Satellite & Geospatial Data
rasterio>=1.4.0
geopandas>=1.0.0
folium>=0.17.0
sentinelhub>=3.10.0

# Patent & Innovation Data
google-api-python-client>=2.150.0
uspto-api>=1.0.0

# Real-time Data Processing
websockets>=13.0
asyncio-mqtt>=0.16.0
redis>=5.1.0
celery>=5.4.0

# Advanced Analytics
plotly>=5.24.0
dash>=2.18.0
streamlit>=1.39.0
bokeh>=3.6.0

# Machine Learning Enhancement
torch>=2.5.0
transformers>=4.46.0
sentence-transformers>=3.2.0
huggingface-hub>=0.26.0

# Time Series & Forecasting
prophet>=1.1.5
statsmodels>=0.14.4
arch>=7.1.0

# Database & Storage
sqlalchemy>=2.0.36
psycopg2-binary>=2.9.9
pymongo>=4.10.0
redis>=5.1.0

# API & Web Framework
fastapi>=0.115.0
uvicorn>=0.32.0
pydantic>=2.9.0
httpx>=0.27.0

# Monitoring & Logging
structlog>=24.4.0
prometheus-client>=0.21.0
grafana-api>=1.0.3

# Testing & Quality
pytest>=8.3.0
pytest-asyncio>=0.24.0
black>=24.10.0
flake8>=7.1.0
mypy>=1.13.0

# Security & Environment
python-dotenv>=1.0.1
cryptography>=43.0.0
pyjwt>=2.9.0

# Deployment & Containerization
docker>=7.1.0
kubernetes>=31.0.0
gunicorn>=23.0.0

# Documentation
sphinx>=8.1.0
mkdocs>=1.6.0
jupyter>=1.1.0

# Development Tools
ipython>=8.29.0
notebook>=7.2.0
jupyterlab>=4.3.0
