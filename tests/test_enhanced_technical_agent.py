"""
Test suite for Enhanced Technical Analysis Agent
Tests comprehensive technical indicators, pattern recognition, and multi-timeframe analysis
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch
import asyncio

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.agents.technical import EnhancedTechnicalAnalysisAgent
from src.agents.base import AgentResult


class TestEnhancedTechnicalAnalysisAgent:
    """Test suite for Enhanced Technical Analysis Agent"""
    
    @pytest.fixture
    def sample_config(self):
        """Sample configuration for enhanced technical agent"""
        return {
            'lookback_period': 200,
            'min_pattern_strength': 0.7,
            'signal_threshold': 0.6,
            'risk_tolerance': 0.02,
            'timeframes': ['1m', '5m', '15m', '1h', '4h', '1d'],
            'enable_ml_models': True,
            'enable_harmonic_patterns': True,
            'enable_elliott_wave': True,
            'enable_fractal_analysis': True
        }
    
    @pytest.fixture
    def enhanced_agent(self, sample_config):
        """Create enhanced technical analysis agent instance"""
        return EnhancedTechnicalAnalysisAgent(sample_config)
    
    @pytest.fixture
    def sample_ohlcv_data(self):
        """Generate sample OHLCV data for testing"""
        dates = pd.date_range(start='2023-01-01', end='2024-12-31', freq='D')
        np.random.seed(42)  # For reproducible tests
        
        # Generate realistic price data
        base_price = 100
        returns = np.random.normal(0.0005, 0.02, len(dates))
        prices = base_price * np.cumprod(1 + returns)
        
        # Generate OHLC
        open_prices = np.roll(prices, 1)
        open_prices[0] = prices[0]
        
        daily_range = np.abs(np.random.normal(0, 0.01, len(dates)))
        high = prices * (1 + daily_range)
        low = prices * (1 - daily_range)
        
        # Ensure OHLC consistency
        high = np.maximum(high, np.maximum(open_prices, prices))
        low = np.minimum(low, np.minimum(open_prices, prices))
        
        # Generate volume
        volume = np.random.lognormal(15, 0.5, len(dates))
        
        return pd.DataFrame({
            'open': open_prices,
            'high': high,
            'low': low,
            'close': prices,
            'volume': volume
        }, index=dates)
    
    @pytest.fixture
    def multi_timeframe_data(self, sample_ohlcv_data):
        """Generate multi-timeframe sample data"""
        return {
            '1d': {'BTC': sample_ohlcv_data},
            '4h': {'BTC': sample_ohlcv_data.iloc[-200:]},  # Shorter for higher timeframes
            '1h': {'BTC': sample_ohlcv_data.iloc[-168:]}
        }
    
    def test_agent_initialization(self, enhanced_agent):
        """Test enhanced technical agent initialization"""
        assert enhanced_agent.agent_name == "enhanced_technical_analysis"
        assert enhanced_agent.timeframes is not None
        assert enhanced_agent.indicators is not None
        assert enhanced_agent.patterns is not None
        assert enhanced_agent.signal_config is not None
        assert enhanced_agent.analytics is not None
        
        # Check if advanced configurations are set
        assert 'harmonic_patterns' in enhanced_agent.patterns
        assert 'elliott_wave' in enhanced_agent.analytics
        assert 'fractal_analysis' in enhanced_agent.analytics
    
    def test_config_validation(self, sample_config):
        """Test configuration validation"""
        agent = EnhancedTechnicalAnalysisAgent(sample_config)
        assert agent._validate_config() is True
        
        # Test with invalid config
        invalid_config = sample_config.copy()
        invalid_config['lookback_period'] = 10  # Too small
        agent_invalid = EnhancedTechnicalAnalysisAgent(invalid_config)
        assert agent_invalid._validate_config() is False
    
    def test_data_quality_assessment(self, enhanced_agent, multi_timeframe_data):
        """Test enhanced data quality assessment"""
        quality_score = enhanced_agent._assess_enhanced_data_quality(multi_timeframe_data)
        
        assert isinstance(quality_score, float)
        assert 0.0 <= quality_score <= 1.0
        assert quality_score > 0.5  # Should be decent for well-formed data
    
    def test_ohlc_consistency_check(self, enhanced_agent, sample_ohlcv_data):
        """Test OHLC data consistency checking"""
        consistency = enhanced_agent._check_ohlc_consistency(sample_ohlcv_data)
        
        assert isinstance(consistency, float)
        assert 0.0 <= consistency <= 1.0
        assert consistency > 0.95  # Should be very high for valid OHLC data
    
    def test_data_continuity_check(self, enhanced_agent, sample_ohlcv_data):
        """Test data continuity checking"""
        continuity = enhanced_agent._check_data_continuity(sample_ohlcv_data)
        
        assert isinstance(continuity, float)
        assert 0.0 <= continuity <= 1.0
    
    @pytest.mark.asyncio
    async def test_enhanced_rsi_calculation(self, enhanced_agent, sample_ohlcv_data):
        """Test enhanced RSI calculation"""
        close = sample_ohlcv_data['close']
        rsi_result = enhanced_agent._calculate_enhanced_rsi(close, 14)
        
        assert 'rsi' in rsi_result
        assert 'rsi_smoothed' in rsi_result
        assert 'divergence' in rsi_result
        assert 'overbought' in rsi_result
        assert 'oversold' in rsi_result
        
        rsi = rsi_result['rsi']
        assert isinstance(rsi, pd.Series)
        assert not rsi.isna().all()
        
        # RSI should be between 0 and 100
        valid_rsi = rsi.dropna()
        if len(valid_rsi) > 0:
            assert valid_rsi.min() >= 0
            assert valid_rsi.max() <= 100
    
    @pytest.mark.asyncio
    async def test_enhanced_adx_calculation(self, enhanced_agent, sample_ohlcv_data):
        """Test enhanced ADX calculation"""
        adx_result = enhanced_agent._calculate_enhanced_adx(sample_ohlcv_data)
        
        assert 'adx' in adx_result
        assert 'di_plus' in adx_result
        assert 'di_minus' in adx_result
        assert 'trend_strength' in adx_result
        
        adx = adx_result['adx']
        assert isinstance(adx, pd.Series)
    
    @pytest.mark.asyncio
    async def test_parabolic_sar_calculation(self, enhanced_agent, sample_ohlcv_data):
        """Test enhanced Parabolic SAR calculation"""
        config = {'af_start': 0.02, 'af_increment': 0.02, 'af_max': 0.2}
        sar_result = enhanced_agent._calculate_enhanced_parabolic_sar(sample_ohlcv_data, config)
        
        assert 'sar' in sar_result
        assert 'trend' in sar_result
        assert 'af' in sar_result
        assert 'extreme_point' in sar_result
        
        sar = sar_result['sar']
        trend = sar_result['trend']
        
        assert isinstance(sar, pd.Series)
        assert isinstance(trend, pd.Series)
        
        # Trend should be 1 or -1
        valid_trend = trend.dropna()
        if len(valid_trend) > 0:
            assert set(valid_trend.unique()).issubset({-1, 1})
    
    @pytest.mark.asyncio
    async def test_ichimoku_cloud_calculation(self, enhanced_agent, sample_ohlcv_data):
        """Test Ichimoku Cloud calculation"""
        config = {'tenkan': 9, 'kijun': 26, 'senkou_b': 52}
        ichimoku_result = enhanced_agent._calculate_ichimoku_cloud(sample_ohlcv_data, config)
        
        expected_components = [
            'tenkan_sen', 'kijun_sen', 'senkou_span_a', 'senkou_span_b',
            'chikou_span', 'cloud_top', 'cloud_bottom', 'cloud_color'
        ]
        
        for component in expected_components:
            assert component in ichimoku_result
    
    @pytest.mark.asyncio
    async def test_bollinger_bands_enhanced(self, enhanced_agent, sample_ohlcv_data):
        """Test enhanced Bollinger Bands calculation"""
        close = sample_ohlcv_data['close']
        config = {'period': 20, 'std': 2}
        bb_result = enhanced_agent._calculate_enhanced_bollinger_bands(close, config)
        
        assert 'middle' in bb_result
        assert 'upper' in bb_result
        assert 'lower' in bb_result
        assert 'bandwidth' in bb_result
        assert 'percent_b' in bb_result
        assert 'squeeze' in bb_result
        
        upper = bb_result['upper']
        middle = bb_result['middle']
        lower = bb_result['lower']
        
        # Upper should be >= middle >= lower
        valid_data = ~(upper.isna() | middle.isna() | lower.isna())
        if valid_data.sum() > 0:
            assert (upper[valid_data] >= middle[valid_data]).all()
            assert (middle[valid_data] >= lower[valid_data]).all()
    
    @pytest.mark.asyncio
    async def test_candlestick_pattern_detection(self, enhanced_agent, sample_ohlcv_data):
        """Test candlestick pattern detection"""
        patterns = await enhanced_agent._detect_enhanced_candlestick_patterns(sample_ohlcv_data)
        
        expected_patterns = [
            'doji', 'hammer', 'shooting_star', 'spinning_top',
            'engulfing_bullish', 'engulfing_bearish', 'harami_bullish', 'harami_bearish',
            'piercing_line', 'dark_cloud_cover', 'morning_star', 'evening_star',
            'three_white_soldiers', 'three_black_crows'
        ]
        
        for pattern in expected_patterns:
            assert pattern in patterns
    
    def test_doji_pattern_detection(self, enhanced_agent, sample_ohlcv_data):
        """Test Doji pattern detection specifically"""
        doji_pattern = enhanced_agent._detect_doji_pattern(sample_ohlcv_data)
        
        assert isinstance(doji_pattern, pd.Series)
        assert doji_pattern.dtype == bool
        assert len(doji_pattern) == len(sample_ohlcv_data)
    
    @pytest.mark.asyncio
    async def test_comprehensive_indicator_calculation(self, enhanced_agent, multi_timeframe_data):
        """Test comprehensive technical indicator calculation"""
        indicators = await enhanced_agent._calculate_comprehensive_indicators(multi_timeframe_data)
        
        assert isinstance(indicators, dict)
        
        # Check that indicators exist for each timeframe
        for timeframe in multi_timeframe_data.keys():
            assert timeframe in indicators
            assert 'BTC' in indicators[timeframe]
            
            btc_indicators = indicators[timeframe]['BTC']
            assert 'trend' in btc_indicators
            assert 'momentum' in btc_indicators
            assert 'volatility' in btc_indicators
            assert 'volume' in btc_indicators
            assert 'market_structure' in btc_indicators
    
    @pytest.mark.asyncio
    async def test_pattern_recognition_workflow(self, enhanced_agent, multi_timeframe_data):
        """Test complete pattern recognition workflow"""
        primary_data = multi_timeframe_data['1d']
        pattern_analysis = await enhanced_agent._perform_advanced_pattern_recognition(primary_data)
        
        assert 'detected_patterns' in pattern_analysis
        assert 'pattern_summary' in pattern_analysis
        assert 'pattern_confidence' in pattern_analysis
        
        detected_patterns = pattern_analysis['detected_patterns']
        if 'BTC' in detected_patterns:
            btc_patterns = detected_patterns['BTC']
            expected_categories = ['reversal', 'continuation', 'candlestick']
            for category in expected_categories:
                assert category in btc_patterns
    
    @pytest.mark.asyncio
    async def test_market_structure_analysis(self, enhanced_agent, multi_timeframe_data):
        """Test market structure analysis"""
        primary_data = multi_timeframe_data['1d']
        market_structure = await enhanced_agent._analyze_market_structure(primary_data)
        
        assert isinstance(market_structure, dict)
        
        if 'BTC' in market_structure:
            btc_structure = market_structure['BTC']
            expected_components = ['support_resistance', 'trend_analysis', 'volume_profile']
            for component in expected_components:
                assert component in btc_structure
    
    @pytest.mark.asyncio
    async def test_multi_timeframe_signal_synthesis(self, enhanced_agent, multi_timeframe_data):
        """Test multi-timeframe signal synthesis"""
        # First calculate indicators
        technical_indicators = await enhanced_agent._calculate_comprehensive_indicators(multi_timeframe_data)
        
        # Then synthesize signals
        mtf_signals = await enhanced_agent._synthesize_multi_timeframe_signals(
            multi_timeframe_data, technical_indicators
        )
        
        assert isinstance(mtf_signals, dict)
        
        if 'BTC' in mtf_signals:
            btc_signals = mtf_signals['BTC']
            expected_components = [
                'timeframe_signals', 'weighted_signal', 'signal_consensus', 'timeframe_alignment'
            ]
            for component in expected_components:
                assert component in btc_signals
    
    @pytest.mark.asyncio
    async def test_risk_analysis(self, enhanced_agent, multi_timeframe_data):
        """Test comprehensive risk analysis"""
        primary_data = multi_timeframe_data['1d']
        technical_indicators = await enhanced_agent._calculate_comprehensive_indicators(multi_timeframe_data)
        
        # Mock trading signals for risk analysis
        trading_signals = {'BTC': {'combined_signal': {'signal': 0.5, 'strength': 0.7}}}
        
        risk_analysis = await enhanced_agent._perform_risk_analysis(
            primary_data, technical_indicators, trading_signals
        )
        
        assert isinstance(risk_analysis, dict)
        
        if 'BTC' in risk_analysis:
            btc_risk = risk_analysis['BTC']
            expected_components = [
                'volatility_risk', 'drawdown_risk', 'liquidity_risk', 
                'technical_risk', 'risk_adjusted_metrics'
            ]
            for component in expected_components:
                assert component in btc_risk
    
    @pytest.mark.asyncio
    async def test_complete_analysis_workflow(self, enhanced_agent):
        """Test complete enhanced technical analysis workflow"""
        symbols = ['BTC', 'ETH']
        timeframe = '1d'
        
        # Mock the data collection to avoid external API calls
        with patch.object(enhanced_agent, '_collect_multi_timeframe_data') as mock_collect:
            mock_data = {
                '1d': {
                    'BTC': enhanced_agent._generate_sample_symbol_ohlcv('BTC'),
                    'ETH': enhanced_agent._generate_sample_symbol_ohlcv('ETH')
                }
            }
            mock_collect.return_value = mock_data
            
            # Run complete analysis
            result = await enhanced_agent._perform_analysis(symbols, timeframe)
            
            # Verify result structure
            assert isinstance(result, AgentResult)
            assert result.agent_name == "enhanced_technical_analysis"
            assert result.analysis_type == "enhanced_technical_analysis"
            assert result.symbols == symbols
            assert 0.0 <= result.confidence <= 1.0
            
            # Check predictions structure
            predictions = result.predictions
            expected_prediction_keys = [
                'technical_indicators', 'pattern_analysis', 'market_structure',
                'multi_timeframe_signals', 'trading_signals', 'risk_analysis',
                'performance_analysis', 'trend_projections', 'volatility_forecast',
                'support_resistance_levels'
            ]
            
            for key in expected_prediction_keys:
                assert key in predictions
            
            # Check recommendations and risk factors
            assert isinstance(result.recommendations, list)
            assert isinstance(result.risk_factors, list)
            assert len(result.recommendations) > 0
            assert len(result.risk_factors) > 0
            
            # Check metadata
            metadata = result.metadata
            assert 'timeframes_analyzed' in metadata
            assert 'indicators_calculated' in metadata
            assert 'patterns_detected' in metadata
            assert 'primary_timeframe' in metadata
            assert 'analysis_depth' in metadata
            assert metadata['analysis_depth'] == 'comprehensive'
    
    def test_sample_data_generation(self, enhanced_agent):
        """Test sample data generation"""
        symbols = ['BTC', 'ETH', 'SPY']
        lookback = 100
        
        sample_data = enhanced_agent._generate_sample_ohlcv_data(symbols, lookback)
        
        assert isinstance(sample_data, dict)
        assert len(sample_data) == len(symbols)
        
        for symbol in symbols:
            assert symbol in sample_data
            data = sample_data[symbol]
            
            assert isinstance(data, pd.DataFrame)
            assert len(data) == lookback
            
            # Check required columns
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                assert col in data.columns
            
            # Check OHLC consistency
            assert (data['high'] >= data['low']).all()
            assert (data['high'] >= data['close']).all()
            assert (data['high'] >= data['open']).all()
            assert (data['close'] >= data['low']).all()
            assert (data['open'] >= data['low']).all()
            
            # Check positive volume
            assert (data['volume'] > 0).all()
    
    @pytest.mark.asyncio
    async def test_error_handling(self, enhanced_agent):
        """Test error handling in analysis"""
        # Test with empty data
        empty_data = {}
        
        # Should not raise exceptions, but return default values
        result = await enhanced_agent._calculate_comprehensive_indicators({'1d': empty_data})
        assert isinstance(result, dict)
        
        pattern_result = await enhanced_agent._perform_advanced_pattern_recognition(empty_data)
        assert isinstance(pattern_result, dict)
        
        structure_result = await enhanced_agent._analyze_market_structure(empty_data)
        assert isinstance(structure_result, dict)
    
    def test_performance_metrics(self, enhanced_agent):
        """Test performance tracking metrics initialization"""
        metrics = enhanced_agent.performance_metrics
        
        expected_metrics = ['accuracy', 'sharpe_ratio', 'max_drawdown', 'win_rate', 'profit_factor']
        for metric in expected_metrics:
            assert metric in metrics
            assert isinstance(metrics[metric], (int, float))


@pytest.mark.integration
class TestEnhancedTechnicalAgentIntegration:
    """Integration tests for Enhanced Technical Analysis Agent"""
    
    @pytest.mark.asyncio
    async def test_full_analysis_pipeline(self):
        """Test full analysis pipeline integration"""
        config = {
            'lookback_period': 100,
            'min_pattern_strength': 0.6,
            'signal_threshold': 0.5,
            'risk_tolerance': 0.02
        }
        
        agent = EnhancedTechnicalAnalysisAgent(config)
        
        # Test with minimal setup (no external dependencies)
        symbols = ['TEST_SYMBOL']
        timeframe = '1d'
        
        with patch.object(agent, '_collect_multi_timeframe_data') as mock_collect:
            mock_data = {
                '1d': {'TEST_SYMBOL': agent._generate_sample_symbol_ohlcv('TEST_SYMBOL')}
            }
            mock_collect.return_value = mock_data
            
            result = await agent._perform_analysis(symbols, timeframe)
            
            # Verify complete result
            assert isinstance(result, AgentResult)
            assert result.confidence > 0
            assert len(result.recommendations) > 0
            assert len(result.risk_factors) > 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
