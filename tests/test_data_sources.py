"""
Comprehensive test suite for data sources
Production-grade testing with real API validation
"""

import pytest
import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
import json
import requests

from src.data.sources import (
    AlphaVantageSource, FinnhubSource, FREDSource, DataSourceRegistry,
    DataSourceError, RateLimitExceeded, DataQualityError, RateLimiter
)
from src.core.system import MarketData, credentials, validator

class TestRateLimiter:
    """Test rate limiting functionality"""
    
    def test_rate_limiter_initialization(self):
        """Test rate limiter initialization"""
        limiter = RateLimiter(calls_per_minute=60, calls_per_hour=1000, calls_per_day=10000)
        assert limiter.calls_per_minute == 60
        assert limiter.calls_per_hour == 1000
        assert limiter.calls_per_day == 10000
        assert len(limiter.minute_calls) == 0
    
    def test_rate_limiter_allows_initial_calls(self):
        """Test that rate limiter allows initial calls"""
        limiter = RateLimiter(calls_per_minute=60, calls_per_hour=1000, calls_per_day=10000)
        assert limiter.can_make_call() is True
    
    def test_rate_limiter_respects_minute_limit(self):
        """Test minute-level rate limiting"""
        limiter = RateLimiter(calls_per_minute=2, calls_per_hour=1000, calls_per_day=10000)
        
        # First two calls should be allowed
        assert limiter.can_make_call() is True
        limiter.record_call()
        assert limiter.can_make_call() is True
        limiter.record_call()
        
        # Third call should be blocked
        assert limiter.can_make_call() is False
    
    def test_rate_limiter_cleanup(self):
        """Test that old calls are cleaned up"""
        limiter = RateLimiter(calls_per_minute=2, calls_per_hour=1000, calls_per_day=10000)
        
        # Add old calls (simulate past time)
        old_time = datetime.now() - timedelta(minutes=2)
        limiter.minute_calls = [old_time, old_time]
        
        # Should allow new calls after cleanup
        assert limiter.can_make_call() is True

class TestDataValidation:
    """Test data validation functionality"""
    
    def test_price_data_validation_success(self):
        """Test successful price data validation"""
        data = pd.DataFrame({
            'open': [100, 101, 102],
            'high': [105, 106, 107],
            'low': [95, 96, 97],
            'close': [104, 105, 106],
            'volume': [1000, 1100, 1200]
        }, index=pd.date_range('2023-01-01', periods=3))
        
        is_valid, metrics = validator.validate_price_data(data)
        assert is_valid is True
        assert len(metrics['issues']) == 0
    
    def test_price_data_validation_negative_prices(self):
        """Test detection of negative prices"""
        data = pd.DataFrame({
            'open': [100, -101, 102],  # Negative price
            'high': [105, 106, 107],
            'low': [95, 96, 97],
            'close': [104, 105, 106],
            'volume': [1000, 1100, 1200]
        }, index=pd.date_range('2023-01-01', periods=3))
        
        is_valid, metrics = validator.validate_price_data(data)
        assert is_valid is False
        assert any('Negative prices' in issue for issue in metrics['issues'])
    
    def test_price_data_validation_high_low_violation(self):
        """Test detection of high < low violations"""
        data = pd.DataFrame({
            'open': [100, 101, 102],
            'high': [95, 106, 107],  # High < Low
            'low': [105, 96, 97],
            'close': [104, 105, 106],
            'volume': [1000, 1100, 1200]
        }, index=pd.date_range('2023-01-01', periods=3))
        
        is_valid, metrics = validator.validate_price_data(data)
        assert is_valid is False
        assert any('High < Low violation' in issue for issue in metrics['issues'])
    
    def test_fundamental_data_validation_success(self):
        """Test successful fundamental data validation"""
        data = {
            'revenue': 1000000,
            'netIncome': 100000,
            'totalAssets': 5000000,
            'totalDebt': 2000000
        }
        
        is_valid, metrics = validator.validate_fundamental_data(data)
        assert is_valid is True
        assert len(metrics['issues']) == 0
    
    def test_fundamental_data_validation_missing_fields(self):
        """Test detection of missing fundamental fields"""
        data = {
            'revenue': 1000000,
            # Missing other required fields
        }
        
        is_valid, metrics = validator.validate_fundamental_data(data)
        assert is_valid is False
        assert any('Missing fields' in issue for issue in metrics['issues'])

class TestAlphaVantageSource:
    """Test Alpha Vantage data source"""
    
    @pytest.fixture
    def alpha_vantage_source(self):
        """Create Alpha Vantage source for testing"""
        return AlphaVantageSource()
    
    @pytest.fixture
    def mock_alpha_vantage_response(self):
        """Mock Alpha Vantage API response"""
        return {
            "Meta Data": {
                "1. Information": "Daily Prices",
                "2. Symbol": "AAPL",
                "3. Last Refreshed": "2023-12-01",
                "4. Output Size": "Compact",
                "5. Time Zone": "US/Eastern"
            },
            "Time Series (Daily)": {
                "2023-12-01": {
                    "1. open": "189.8400",
                    "2. high": "190.6700",
                    "3. low": "189.4300",
                    "4. close": "190.3300",
                    "5. volume": "48744000"
                },
                "2023-11-30": {
                    "1. open": "189.9200",
                    "2. high": "190.8000",
                    "3. low": "188.9700",
                    "4. close": "189.9500",
                    "5. volume": "51131200"
                }
            }
        }
    
    def test_alpha_vantage_initialization(self, alpha_vantage_source):
        """Test Alpha Vantage source initialization"""
        assert alpha_vantage_source.name == "AlphaVantage"
        assert alpha_vantage_source.api_key == credentials.alpha_vantage_api_key
        assert alpha_vantage_source.base_url == "https://www.alphavantage.co/query"
    
    @patch('requests.Session.get')
    async def test_alpha_vantage_fetch_success(self, mock_get, alpha_vantage_source, mock_alpha_vantage_response):
        """Test successful Alpha Vantage data fetch"""
        # Mock response
        mock_response = Mock()
        mock_response.json.return_value = mock_alpha_vantage_response
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        # Test fetch
        result = await alpha_vantage_source.fetch_data("AAPL")
        
        assert isinstance(result, MarketData)
        assert result.symbol == "AAPL"
        assert result.source == "AlphaVantage"
        assert result.data_type == "price"
        assert 'price_data' in result.data
        assert 'metadata' in result.data
        assert result.quality_score > 0
    
    @patch('requests.Session.get')
    async def test_alpha_vantage_api_error(self, mock_get, alpha_vantage_source):
        """Test Alpha Vantage API error handling"""
        # Mock error response
        error_response = {"Error Message": "Invalid API call"}
        mock_response = Mock()
        mock_response.json.return_value = error_response
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        # Test that error is raised
        with pytest.raises(DataSourceError, match="Alpha Vantage error"):
            await alpha_vantage_source.fetch_data("INVALID")
    
    @patch('requests.Session.get')
    async def test_alpha_vantage_rate_limit(self, mock_get, alpha_vantage_source):
        """Test Alpha Vantage rate limit handling"""
        # Mock rate limit response
        rate_limit_response = {"Note": "Thank you for using Alpha Vantage! Our standard API call frequency is 5 calls per minute"}
        mock_response = Mock()
        mock_response.json.return_value = rate_limit_response
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        # Test that rate limit error is raised
        with pytest.raises(RateLimitExceeded):
            await alpha_vantage_source.fetch_data("AAPL")
    
    def test_alpha_vantage_parse_time_series(self, alpha_vantage_source, mock_alpha_vantage_response):
        """Test parsing of Alpha Vantage time series data"""
        parsed = alpha_vantage_source._parse_time_series_data(mock_alpha_vantage_response, "AAPL")
        
        assert 'price_data' in parsed
        assert 'metadata' in parsed
        assert parsed['metadata']['symbol'] == "AAPL"
        assert parsed['metadata']['data_points'] == 2
        
        # Check price data structure
        price_data = parsed['price_data']
        assert len(price_data) == 2
        
        # Check data values
        for date_key, values in price_data.items():
            assert 'open' in values
            assert 'high' in values
            assert 'low' in values
            assert 'close' in values
            assert 'volume' in values
            assert isinstance(values['open'], float)
            assert isinstance(values['volume'], int)

class TestFinnhubSource:
    """Test Finnhub data source"""
    
    @pytest.fixture
    def finnhub_source(self):
        """Create Finnhub source for testing"""
        return FinnhubSource()
    
    @pytest.fixture
    def mock_finnhub_quote_response(self):
        """Mock Finnhub quote response"""
        return {
            "c": 190.33,  # Current price
            "d": 0.38,    # Change
            "dp": 0.2,    # Percent change
            "h": 190.67,  # High
            "l": 189.43,  # Low
            "o": 189.84,  # Open
            "pc": 189.95, # Previous close
            "t": 1701453600  # Timestamp
        }
    
    def test_finnhub_initialization(self, finnhub_source):
        """Test Finnhub source initialization"""
        assert finnhub_source.name == "Finnhub"
        assert finnhub_source.api_key == credentials.finnhub_api_key
        assert finnhub_source.base_url == "https://finnhub.io/api/v1"
    
    @patch('requests.Session.get')
    async def test_finnhub_fetch_quote_success(self, mock_get, finnhub_source, mock_finnhub_quote_response):
        """Test successful Finnhub quote fetch"""
        # Mock response
        mock_response = Mock()
        mock_response.json.return_value = mock_finnhub_quote_response
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        # Test fetch
        result = await finnhub_source.fetch_data("AAPL", data_type="quote")
        
        assert isinstance(result, MarketData)
        assert result.symbol == "AAPL"
        assert result.source == "Finnhub"
        assert result.data_type == "quote"
        assert 'current_price' in result.data
        assert result.data['current_price'] == 190.33
        assert result.quality_score > 0

class TestFREDSource:
    """Test FRED data source"""
    
    @pytest.fixture
    def fred_source(self):
        """Create FRED source for testing"""
        return FREDSource()
    
    @pytest.fixture
    def mock_fred_response(self):
        """Mock FRED API response"""
        return {
            "realtime_start": "2023-12-01",
            "realtime_end": "2023-12-01",
            "observation_start": "1776-07-04",
            "observation_end": "9999-12-31",
            "units": "lin",
            "output_type": 1,
            "file_type": "json",
            "order_by": "observation_date",
            "sort_order": "asc",
            "count": 2,
            "offset": 0,
            "limit": 100000,
            "observations": [
                {
                    "realtime_start": "2023-12-01",
                    "realtime_end": "2023-12-01",
                    "date": "2023-11-01",
                    "value": "5.25"
                },
                {
                    "realtime_start": "2023-12-01",
                    "realtime_end": "2023-12-01",
                    "date": "2023-12-01",
                    "value": "5.50"
                }
            ]
        }
    
    def test_fred_initialization(self, fred_source):
        """Test FRED source initialization"""
        assert fred_source.name == "FRED"
        assert fred_source.api_key == credentials.fred_api_key
        assert fred_source.base_url == "https://api.stlouisfed.org/fred"
    
    @patch('requests.Session.get')
    async def test_fred_fetch_success(self, mock_get, fred_source, mock_fred_response):
        """Test successful FRED data fetch"""
        # Mock response
        mock_response = Mock()
        mock_response.json.return_value = mock_fred_response
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        # Test fetch
        result = await fred_source.fetch_data("FEDFUNDS")
        
        assert isinstance(result, MarketData)
        assert result.symbol == "FEDFUNDS"
        assert result.source == "FRED"
        assert result.data_type == "macro"
        assert 'series_data' in result.data
        assert 'metadata' in result.data
        assert result.data['metadata']['data_points'] == 2

class TestDataSourceRegistry:
    """Test data source registry"""
    
    def test_registry_initialization(self):
        """Test registry initialization"""
        registry = DataSourceRegistry()
        assert len(registry.sources) > 0
        assert isinstance(registry.list_sources(), list)
    
    def test_get_source(self):
        """Test getting specific source"""
        registry = DataSourceRegistry()
        
        if credentials.alpha_vantage_api_key:
            source = registry.get_source('alpha_vantage')
            assert source is not None
            assert isinstance(source, AlphaVantageSource)
        
        # Test invalid source
        invalid_source = registry.get_source('invalid_source')
        assert invalid_source is None

# Integration tests (only run if API keys are available)
class TestIntegrationWithRealAPIs:
    """Integration tests with real APIs - only run when API keys are available"""
    
    @pytest.mark.skipif(not credentials.alpha_vantage_api_key, reason="Alpha Vantage API key not available")
    @pytest.mark.integration
    async def test_real_alpha_vantage_api(self):
        """Test with real Alpha Vantage API"""
        source = AlphaVantageSource()
        
        try:
            result = await source.fetch_data("AAPL")
            assert isinstance(result, MarketData)
            assert result.symbol == "AAPL"
            assert result.source == "AlphaVantage"
            assert 'price_data' in result.data
        except RateLimitExceeded:
            pytest.skip("Rate limit exceeded for Alpha Vantage")
        except DataSourceError as e:
            pytest.fail(f"API call failed: {e}")
    
    @pytest.mark.skipif(not credentials.finnhub_api_key, reason="Finnhub API key not available")
    @pytest.mark.integration
    async def test_real_finnhub_api(self):
        """Test with real Finnhub API"""
        source = FinnhubSource()
        
        try:
            result = await source.fetch_data("AAPL", data_type="quote")
            assert isinstance(result, MarketData)
            assert result.symbol == "AAPL"
            assert result.source == "Finnhub"
            assert 'current_price' in result.data
            assert result.data['current_price'] > 0
        except RateLimitExceeded:
            pytest.skip("Rate limit exceeded for Finnhub")
        except DataSourceError as e:
            pytest.fail(f"API call failed: {e}")
    
    @pytest.mark.skipif(not credentials.fred_api_key, reason="FRED API key not available")
    @pytest.mark.integration
    async def test_real_fred_api(self):
        """Test with real FRED API"""
        source = FREDSource()
        
        try:
            result = await source.fetch_data("FEDFUNDS")
            assert isinstance(result, MarketData)
            assert result.symbol == "FEDFUNDS"
            assert result.source == "FRED"
            assert 'series_data' in result.data
        except DataSourceError as e:
            pytest.fail(f"API call failed: {e}")

# Performance tests
class TestPerformance:
    """Performance and load testing"""
    
    @pytest.mark.performance
    async def test_concurrent_data_fetching(self):
        """Test concurrent data fetching performance"""
        registry = DataSourceRegistry()
        
        # Create multiple concurrent requests
        symbols = ["AAPL", "MSFT", "GOOGL", "AMZN", "TSLA"]
        start_time = datetime.now()
        
        tasks = []
        for symbol in symbols:
            task = registry.fetch_multi_source_data(symbol)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # Should complete within reasonable time (adjust based on requirements)
        assert duration < 60  # 60 seconds for 5 symbols
        
        # Check results
        successful_results = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_results) > 0

if __name__ == "__main__":
    # Run basic tests
    pytest.main([__file__, "-v"])
    
    # Run integration tests (if API keys available)
    # pytest.main([__file__, "-v", "-m", "integration"])
    
    # Run performance tests
    # pytest.main([__file__, "-v", "-m", "performance"])
