#!/usr/bin/env python3
"""
Dr. Apex Mathematical Model Validation
IEEE 754 compliance and numerical stability testing
"""

import numpy as np
import pandas as pd
from scipy import stats, linalg
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def test_ieee_754_compliance():
    """Test IEEE 754 floating point compliance"""
    print('🔬 Testing IEEE 754 Compliance...')
    
    tests = {
        'infinity_handling': np.isinf(np.inf),
        'nan_handling': np.isnan(np.nan),
        'negative_zero': np.signbit(-0.0),
        'subnormal_numbers': np.finfo(float).tiny > 0
    }

    # Test zero division handling safely
    try:
        with np.errstate(divide='ignore'):
            result = 1.0 / 0.0
            tests['zero_division'] = np.isinf(result)
    except:
        tests['zero_division'] = False
    
    passed = sum(tests.values())
    total = len(tests)
    
    for test_name, result in tests.items():
        status = '✅' if result else '❌'
        print(f'  {status} {test_name}: {result}')
    
    print(f'📊 IEEE 754 Compliance: {passed}/{total} tests passed')
    return passed == total

def test_matrix_operations():
    """Test matrix operations for numerical stability"""
    print('\n🧮 Testing Matrix Operations...')
    
    # Generate test covariance matrix
    np.random.seed(42)
    n = 10
    A = np.random.randn(n, n)
    cov_matrix = A @ A.T  # Guaranteed positive semi-definite
    
    tests = {}
    
    # Test 1: Eigenvalue decomposition
    try:
        eigenvals, eigenvecs = linalg.eigh(cov_matrix)
        tests['eigenvalue_decomp'] = np.all(eigenvals >= -1e-10)  # Allow small numerical errors
    except Exception as e:
        tests['eigenvalue_decomp'] = False
        print(f'    ❌ Eigenvalue decomposition failed: {e}')
    
    # Test 2: Cholesky decomposition
    try:
        L = linalg.cholesky(cov_matrix, lower=True)
        tests['cholesky_decomp'] = np.allclose(L @ L.T, cov_matrix)
    except Exception as e:
        tests['cholesky_decomp'] = False
        print(f'    ❌ Cholesky decomposition failed: {e}')
    
    # Test 3: SVD decomposition
    try:
        U, s, Vt = linalg.svd(cov_matrix)
        tests['svd_decomp'] = np.allclose(U @ np.diag(s) @ Vt, cov_matrix)
    except Exception as e:
        tests['svd_decomp'] = False
        print(f'    ❌ SVD decomposition failed: {e}')
    
    # Test 4: Matrix inversion
    try:
        inv_matrix = linalg.inv(cov_matrix)
        tests['matrix_inversion'] = np.allclose(cov_matrix @ inv_matrix, np.eye(n), atol=1e-10)
    except Exception as e:
        tests['matrix_inversion'] = False
        print(f'    ❌ Matrix inversion failed: {e}')
    
    # Test 5: Condition number
    try:
        cond_num = linalg.cond(cov_matrix)
        tests['condition_number'] = cond_num < 1e12  # Reasonable condition number
        print(f'    📊 Condition number: {cond_num:.2e}')
    except Exception as e:
        tests['condition_number'] = False
        print(f'    ❌ Condition number calculation failed: {e}')
    
    passed = sum(tests.values())
    total = len(tests)
    
    for test_name, result in tests.items():
        if test_name != 'condition_number':  # Already printed above
            status = '✅' if result else '❌'
            print(f'  {status} {test_name}: {result}')
    
    print(f'📊 Matrix Operations: {passed}/{total} tests passed')
    return passed >= 4  # Allow one failure

def test_statistical_calculations():
    """Test statistical calculations for accuracy"""
    print('\n📈 Testing Statistical Calculations...')
    
    # Generate test data
    np.random.seed(42)
    returns = np.random.normal(0.001, 0.02, 252)  # Daily returns for 1 year
    
    tests = {}
    
    # Test 1: Mean calculation
    try:
        mean_return = np.mean(returns)
        tests['mean_calculation'] = not np.isnan(mean_return) and abs(mean_return) < 1.0
        print(f'    📊 Mean return: {mean_return:.6f}')
    except Exception as e:
        tests['mean_calculation'] = False
        print(f'    ❌ Mean calculation failed: {e}')
    
    # Test 2: Volatility calculation
    try:
        volatility = np.std(returns) * np.sqrt(252)  # Annualized
        tests['volatility_calculation'] = 0.01 < volatility < 2.0  # Reasonable range
        print(f'    📊 Annualized volatility: {volatility:.4f}')
    except Exception as e:
        tests['volatility_calculation'] = False
        print(f'    ❌ Volatility calculation failed: {e}')
    
    # Test 3: Sharpe ratio
    try:
        risk_free_rate = 0.02
        sharpe_ratio = (np.mean(returns) * 252 - risk_free_rate) / (np.std(returns) * np.sqrt(252))
        tests['sharpe_ratio'] = not np.isnan(sharpe_ratio) and abs(sharpe_ratio) < 10
        print(f'    📊 Sharpe ratio: {sharpe_ratio:.4f}')
    except Exception as e:
        tests['sharpe_ratio'] = False
        print(f'    ❌ Sharpe ratio calculation failed: {e}')
    
    # Test 4: VaR calculation
    try:
        var_95 = np.percentile(returns, 5)
        var_99 = np.percentile(returns, 1)
        tests['var_calculation'] = var_99 < var_95 < 0  # VaR should be negative and 99% < 95%
        print(f'    📊 VaR 95%: {var_95:.4f}, VaR 99%: {var_99:.4f}')
    except Exception as e:
        tests['var_calculation'] = False
        print(f'    ❌ VaR calculation failed: {e}')
    
    # Test 5: Maximum drawdown
    try:
        cumulative_returns = np.cumprod(1 + returns)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdown = (cumulative_returns - running_max) / running_max
        max_drawdown = np.min(drawdown)
        tests['max_drawdown'] = max_drawdown < 0  # Should be negative
        print(f'    📊 Maximum drawdown: {max_drawdown:.4f}')
    except Exception as e:
        tests['max_drawdown'] = False
        print(f'    ❌ Maximum drawdown calculation failed: {e}')
    
    passed = sum(tests.values())
    total = len(tests)
    
    for test_name, result in tests.items():
        if 'calculation' in test_name or 'ratio' in test_name or 'drawdown' in test_name:
            continue  # Already printed above
        status = '✅' if result else '❌'
        print(f'  {status} {test_name}: {result}')
    
    print(f'📊 Statistical Calculations: {passed}/{total} tests passed')
    return passed >= 4

def test_portfolio_optimization():
    """Test portfolio optimization algorithms"""
    print('\n💼 Testing Portfolio Optimization...')
    
    # Generate test data
    np.random.seed(42)
    n_assets = 5
    returns_data = np.random.multivariate_normal(
        mean=[0.001] * n_assets,
        cov=np.eye(n_assets) * 0.0004 + 0.0001,  # Some correlation
        size=252
    )
    
    expected_returns = np.mean(returns_data, axis=0) * 252
    cov_matrix = np.cov(returns_data.T) * 252
    
    tests = {}
    
    # Test 1: Minimum variance portfolio
    try:
        ones = np.ones((n_assets, 1))
        inv_cov = linalg.inv(cov_matrix)
        weights = (inv_cov @ ones) / (ones.T @ inv_cov @ ones)
        weights = weights.flatten()
        
        # Check constraints
        weight_sum_ok = abs(np.sum(weights) - 1.0) < 1e-10
        all_positive = np.all(weights >= -1e-10)  # Allow small numerical errors
        
        tests['min_variance_portfolio'] = weight_sum_ok and all_positive
        print(f'    📊 Min variance weights sum: {np.sum(weights):.6f}')
        
    except Exception as e:
        tests['min_variance_portfolio'] = False
        print(f'    ❌ Minimum variance portfolio failed: {e}')
    
    # Test 2: Equal weight portfolio
    try:
        equal_weights = np.ones(n_assets) / n_assets
        portfolio_return = np.sum(equal_weights * expected_returns)
        portfolio_risk = np.sqrt(equal_weights.T @ cov_matrix @ equal_weights)
        
        tests['equal_weight_portfolio'] = (
            abs(np.sum(equal_weights) - 1.0) < 1e-10 and
            not np.isnan(portfolio_return) and
            not np.isnan(portfolio_risk) and
            portfolio_risk > 0
        )
        print(f'    📊 Equal weight return: {portfolio_return:.4f}, risk: {portfolio_risk:.4f}')
        
    except Exception as e:
        tests['equal_weight_portfolio'] = False
        print(f'    ❌ Equal weight portfolio failed: {e}')
    
    passed = sum(tests.values())
    total = len(tests)
    
    for test_name, result in tests.items():
        if 'portfolio' not in test_name:
            continue  # Already printed above
        status = '✅' if result else '❌'
        print(f'  {status} {test_name}: {result}')
    
    print(f'📊 Portfolio Optimization: {passed}/{total} tests passed')
    return passed >= 1

def main():
    """Main validation function"""
    print('🔬 Dr. Apex Mathematical Model Validation')
    print('=' * 50)
    
    # Run all tests
    results = {
        'IEEE 754 Compliance': test_ieee_754_compliance(),
        'Matrix Operations': test_matrix_operations(),
        'Statistical Calculations': test_statistical_calculations(),
        'Portfolio Optimization': test_portfolio_optimization()
    }
    
    # Summary
    print('\n🎯 Mathematical Model Validation Summary')
    print('=' * 45)
    
    passed_tests = 0
    for test_name, result in results.items():
        status = '✅' if result else '❌'
        print(f'{status} {test_name}: {"PASSED" if result else "FAILED"}')
        if result:
            passed_tests += 1
    
    print(f'\n📊 Overall Score: {passed_tests}/{len(results)} test suites passed')
    
    if passed_tests >= 3:
        print('✅ MATHEMATICAL FOUNDATIONS: ACCEPTABLE FOR PRODUCTION')
    else:
        print('❌ MATHEMATICAL FOUNDATIONS: REQUIRE FIXES BEFORE PRODUCTION')
    
    return passed_tests >= 3

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
