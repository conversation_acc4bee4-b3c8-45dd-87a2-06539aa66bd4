#!/usr/bin/env python3
"""
Comprehensive test script for the completed master orchestrator
Tests all major components and methods to ensure they work correctly
"""

import asyncio
import sys
import os

# Add the project root to Python path
sys.path.append('/Users/<USER>/crypto')

from src.orchestration.master_orchestrator import (
    MasterOrchestrator, 
    NoiseManagementSystem, 
    StockForecastingEngine,
    BayesianSynthesizer,
    BlackSwanDetector
)

async def test_noise_management():
    """Test the noise management system"""
    print("=== Testing Noise Management System ===")
    
    try:
        import yfinance as yf
        import pandas as pd
        
        # Get sample data
        ticker = yf.Ticker("AAPL")
        data = ticker.history(period="3mo", interval="1d")
        data.columns = [col.lower() for col in data.columns]
        
        noise_system = NoiseManagementSystem()
        result = noise_system.filter_market_noise(data, "AAPL")
        
        print(f"✓ Noise filtering completed")
        print(f"  - Data quality: {result['data_quality']:.2f}")
        print(f"  - Noise score: {result['noise_score']:.2f}")
        print(f"  - Filtered data points: {len(result['filtered_data'])}")
        
        return True
        
    except Exception as e:
        print(f"✗ Noise management test failed: {e}")
        return False

async def test_stock_forecasting():
    """Test the stock forecasting engine"""
    print("\n=== Testing Stock Forecasting Engine ===")
    
    try:
        import yfinance as yf
        
        # Get sample data
        ticker = yf.Ticker("MSFT")
        data = ticker.history(period="6mo", interval="1d")
        data.columns = [col.lower() for col in data.columns]
        
        forecasting_engine = StockForecastingEngine()
        result = forecasting_engine.generate_stock_forecast("MSFT", data)
        
        print(f"✓ Stock forecasting completed")
        print(f"  - Symbol: {result['symbol']}")
        print(f"  - Forecast quality: {result['forecast_quality']:.2f}")
        print(f"  - Consensus confidence: {result['consensus_forecast']['confidence']:.2f}")
        
        # Test each timeframe
        for timeframe, forecast in result['forecasts_by_timeframe'].items():
            print(f"  - {timeframe}: {forecast['expected_return']:.3f} return, {forecast['confidence']:.2f} confidence")
        
        return True
        
    except Exception as e:
        print(f"✗ Stock forecasting test failed: {e}")
        return False

async def test_bayesian_synthesizer():
    """Test the Bayesian synthesizer"""
    print("\n=== Testing Bayesian Synthesizer ===")
    
    try:
        synthesizer = BayesianSynthesizer()
        
        # Create mock agent results with proper structure
        mock_results = {
            'quantitative': type('AgentResult', (), {
                'symbols': ['AAPL'],
                'predictions': {'price_forecasts': {'AAPL': {'expected_return': 0.05}}},
                'confidence': 0.8,
                'agent_name': 'quantitative'
            })(),
            'technical_analysis': type('AgentResult', (), {
                'symbols': ['AAPL'],
                'predictions': {'trading_signals': {'AAPL': {'combined_signal': {'strength': 0.7, 'direction': 'buy'}}}},
                'confidence': 0.7,
                'agent_name': 'technical_analysis'
            })()
        }
        
        result = synthesizer.synthesize_forecasts(mock_results)
        
        print(f"✓ Bayesian synthesis completed")
        print(f"  - Consensus forecasts available: {'consensus_forecasts' in result}")
        print(f"  - Uncertainty estimates available: {'uncertainty_estimates' in result}")
        print(f"  - Agent weights available: {'agent_weights' in result}")
        
        return True
        
    except Exception as e:
        print(f"✗ Bayesian synthesizer test failed: {e}")
        return False

async def test_black_swan_detector():
    """Test the black swan detector"""
    print("\n=== Testing Black Swan Detector ===")
    
    try:
        detector = BlackSwanDetector()
        
        # Create mock agent results
        mock_results = {
            'agent1': type('AgentResult', (), {
                'forecasts': {'AAPL': 150.0},
                'confidence': 0.8,
                'risk_factors': [],
                'metadata': {'volatility': 0.02}
            })(),
            'agent2': type('AgentResult', (), {
                'forecasts': {'AAPL': 155.0},
                'confidence': 0.7,
                'risk_factors': [],
                'metadata': {'volatility': 0.03}
            })()
        }
        
        # Mock market conditions
        market_conditions = {
            'volatility': 0.025,
            'liquidity': 0.8,
            'correlation': 0.7
        }
        
        risks = detector.detect_black_swan_risks(mock_results, market_conditions)
        
        print(f"✓ Black swan detection completed")
        print(f"  - Number of risks detected: {len(risks)}")
        
        return True
        
    except Exception as e:
        print(f"✗ Black swan detector test failed: {e}")
        return False

async def test_master_orchestrator():
    """Test the master orchestrator end-to-end"""
    print("\n=== Testing Master Orchestrator ===")
    
    try:
        orchestrator = MasterOrchestrator()
        
        # Test single stock analysis
        symbols = ["AAPL", "MSFT"]
        result = await orchestrator.analyze_stock_forecast(symbols, include_noise_analysis=True)
        
        print(f"✓ Master orchestrator analysis completed")
        print(f"  - Symbols analyzed: {len(result.get('stock_forecasts', {}))}")
        print(f"  - Portfolio insights available: {'portfolio_insights' in result}")
        
        # Test individual stock components
        for symbol in symbols[:1]:  # Test first symbol only for brevity
            if symbol in result.get('stock_forecasts', {}):
                stock_result = result['stock_forecasts'][symbol]
                print(f"  - {symbol} data quality: {stock_result.get('data_quality', 'N/A')}")
                
                forecast = stock_result.get('forecast', {})
                if 'consensus_forecast' in forecast:
                    consensus = forecast['consensus_forecast']
                    print(f"  - {symbol} consensus: {consensus.get('expected_return', 0):.3f} return")
        
        return True
        
    except Exception as e:
        print(f"✗ Master orchestrator test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_individual_methods():
    """Test individual methods in isolation"""
    print("\n=== Testing Individual Methods ===")
    
    try:
        orchestrator = MasterOrchestrator()
        
        # Test market data fetching
        print("Testing market data fetch...")
        data = await orchestrator._fetch_market_data("AAPL")
        print(f"✓ Market data fetched: {len(data)} data points")
        
        # Test agent insights
        print("Testing agent insights...")
        insights = await orchestrator._get_agent_insights("AAPL", data)
        print(f"✓ Agent insights: {list(insights.keys())}")
        
        # Test forecast integration
        print("Testing forecast integration...")
        mock_forecast = {
            'symbol': 'AAPL',
            'forecasts_by_timeframe': {
                '1d': {'expected_return': 0.01, 'confidence': 0.7}
            },
            'consensus_forecast': {'expected_return': 0.01, 'confidence': 0.7}
        }
        mock_noise = {'data_quality': 0.8, 'noise_score': 0.2}
        
        integrated = orchestrator._integrate_forecasts(mock_forecast, insights, mock_noise)
        print(f"✓ Forecast integration completed")
        
        return True
        
    except Exception as e:
        print(f"✗ Individual methods test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all tests"""
    print("Stock Forecasting System - Comprehensive Test Suite")
    print("=" * 55)
    
    # Install required packages if needed
    try:
        import yfinance
        import pandas as pd
        import numpy as np
    except ImportError as e:
        print(f"Missing required package: {e}")
        print("Please run: pip install yfinance pandas numpy")
        return
    
    tests = [
        test_noise_management,
        test_stock_forecasting,
        test_bayesian_synthesizer,
        test_black_swan_detector,
        test_individual_methods,
        test_master_orchestrator,
    ]
    
    results = []
    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"Test {test.__name__} crashed: {e}")
            results.append(False)
    
    # Summary
    print("\n" + "=" * 55)
    print("TEST SUMMARY")
    print("=" * 55)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Tests passed: {passed}/{total}")
    print(f"Success rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 All tests passed! The system is ready for use.")
    else:
        print("⚠️  Some tests failed. Please review the errors above.")
    
    print("\nSystem Status: OPERATIONAL" if passed >= total * 0.8 else "System Status: NEEDS ATTENTION")

if __name__ == "__main__":
    asyncio.run(main())
