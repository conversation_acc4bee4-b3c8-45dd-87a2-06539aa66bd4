#!/usr/bin/env python3
"""
Enhanced Confidence Calculation System
Validates the proposed improvements before implementation
"""

import yfinance as yf
import pandas as pd
import numpy as np
from scipy import stats

def analyze_enhanced_confidence():
    """Test enhanced confidence calculations"""
    
    # Get test data
    symbols = ["AAPL", "MSFT", "GOOGL", "TSLA"]
    
    for symbol in symbols:
        print(f"\n=== {symbol} Enhanced Analysis ===")
        
        ticker = yf.Ticker(symbol)
        data = ticker.history(period='6mo', interval='1d')
        
        # Current system signals
        returns = data['Close'].pct_change()
        short_momentum = returns.rolling(window=5).mean().iloc[-1]
        medium_momentum = returns.rolling(window=20).mean().iloc[-1]
        
        # RSI
        delta = data['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        rsi_current = rsi.iloc[-1]
        rsi_signal = (50 - rsi_current) / 100
        
        # Enhanced signal normalization
        # Convert all signals to z-scores (standard deviations from mean)
        momentum_history = returns.rolling(window=5).mean().dropna()
        momentum_zscore = (short_momentum - momentum_history.mean()) / (momentum_history.std() + 1e-8)
        
        medium_momentum_history = returns.rolling(window=20).mean().dropna()
        medium_zscore = (medium_momentum - medium_momentum_history.mean()) / (medium_momentum_history.std() + 1e-8)
        
        # RSI percentile (where current RSI stands in recent history)
        rsi_history = rsi.dropna()
        rsi_percentile = stats.percentileofscore(rsi_history.tail(60), rsi_current) / 100
        rsi_normalized = (rsi_percentile - 0.5) * 2  # Convert to -1 to +1 scale
        
        # Volume confirmation
        volume_mean = data['Volume'].rolling(window=20).mean().iloc[-1]
        recent_volume = data['Volume'].iloc[-5:].mean()
        volume_ratio = recent_volume / volume_mean
        volume_confidence = min(volume_ratio, 2.0) / 2.0  # Cap at 2x average
        
        # Trend strength (R-squared of price trend)
        prices = data['Close'].tail(20).values
        x = np.arange(len(prices))
        slope, intercept, r_value, p_value, std_err = stats.linregress(x, prices)
        trend_strength = abs(r_value)  # R-squared shows trend consistency
        
        # Volatility adjustment
        volatility = returns.rolling(window=20).std().iloc[-1]
        vol_percentile = stats.percentileofscore(returns.rolling(window=20).std().dropna(), volatility) / 100
        volatility_penalty = 1.0 - (vol_percentile * 0.3)  # Reduce confidence in high vol
        
        # Enhanced confidence calculation
        normalized_signals = [momentum_zscore, medium_zscore, rsi_normalized]
        signal_agreement = 1.0 - np.std(normalized_signals) / (np.mean(np.abs(normalized_signals)) + 0.1)
        
        # Combined confidence
        base_confidence = max(signal_agreement, 0.2)  # Floor at 20%
        volume_weighted = base_confidence * (0.7 + 0.3 * volume_confidence)
        trend_weighted = volume_weighted * (0.6 + 0.4 * trend_strength)
        final_confidence = trend_weighted * volatility_penalty
        
        # Original vs Enhanced
        original_agreement = 1.0 - np.std([short_momentum, medium_momentum, rsi_signal]) / (np.mean(np.abs([short_momentum, medium_momentum, rsi_signal])) + 0.01)
        original_confidence = np.clip(original_agreement, 0.3, 0.95)
        
        print(f"Original signals: Mom={short_momentum:.4f}, Med={medium_momentum:.4f}, RSI={rsi_signal:.4f}")
        print(f"Normalized signals: Mom_z={momentum_zscore:.2f}, Med_z={medium_zscore:.2f}, RSI_n={rsi_normalized:.2f}")
        print(f"Volume ratio: {volume_ratio:.2f}, Volume confidence: {volume_confidence:.2f}")
        print(f"Trend strength (R²): {trend_strength:.3f}")
        print(f"Volatility percentile: {vol_percentile:.2f}, Penalty: {volatility_penalty:.2f}")
        print(f"")
        print(f"CONFIDENCE COMPARISON:")
        print(f"Original: {original_confidence:.3f} ({original_confidence*100:.1f}%)")
        print(f"Enhanced: {final_confidence:.3f} ({final_confidence*100:.1f}%)")
        print(f"Improvement: {(final_confidence - original_confidence):.3f}")

if __name__ == "__main__":
    analyze_enhanced_confidence()
