"""
Data Sources Module
Production-ready data acquisition with comprehensive error handling and validation
"""

import asyncio
import aiohttp
import requests
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import time
from abc import ABC, abstractmethod
import json
import structlog
from dataclasses import dataclass

from ..core.system import MarketData, credentials, health_monitor, validator

logger = structlog.get_logger()

class DataSourceError(Exception):
    """Custom exception for data source errors"""
    pass

class RateLimitExceeded(DataSourceError):
    """Exception for rate limit violations"""
    pass

class DataQualityError(DataSourceError):
    """Exception for data quality issues"""
    pass

@dataclass
class RateLimiter:
    """Production-grade rate limiter"""
    calls_per_minute: int
    calls_per_hour: int
    calls_per_day: int
    
    def __post_init__(self):
        self.minute_calls = []
        self.hour_calls = []
        self.day_calls = []
    
    def can_make_call(self) -> bool:
        """Check if we can make a call without exceeding limits"""
        now = datetime.now()
        
        # Clean old calls
        self.minute_calls = [t for t in self.minute_calls if now - t < timedelta(minutes=1)]
        self.hour_calls = [t for t in self.hour_calls if now - t < timedelta(hours=1)]
        self.day_calls = [t for t in self.day_calls if now - t < timedelta(days=1)]
        
        # Check limits
        if len(self.minute_calls) >= self.calls_per_minute:
            return False
        if len(self.hour_calls) >= self.calls_per_hour:
            return False
        if len(self.day_calls) >= self.calls_per_day:
            return False
        
        return True
    
    def record_call(self):
        """Record a successful call"""
        now = datetime.now()
        self.minute_calls.append(now)
        self.hour_calls.append(now)
        self.day_calls.append(now)

class BaseDataSource(ABC):
    """Abstract base class for all data sources"""
    
    def __init__(self, name: str, api_key: str, rate_limiter: RateLimiter):
        self.name = name
        self.api_key = api_key
        self.rate_limiter = rate_limiter
        self.session = requests.Session()
        self._setup_session()
    
    def _setup_session(self):
        """Setup HTTP session with proper headers and timeouts"""
        self.session.headers.update({
            'User-Agent': 'Elite-Market-Analysis-System/1.0',
            'Accept': 'application/json',
            'Connection': 'keep-alive'
        })
        # Set timeouts: (connect_timeout, read_timeout)
        self.session.timeout = (10, 30)
    
    @abstractmethod
    async def fetch_data(self, symbol: str, **kwargs) -> MarketData:
        """Fetch data for a given symbol"""
        pass
    
    def _wait_for_rate_limit(self):
        """Wait if rate limit would be exceeded"""
        while not self.rate_limiter.can_make_call():
            logger.warning(f"Rate limit reached for {self.name}, waiting...")
            time.sleep(1)
        
        self.rate_limiter.record_call()
    
    def _handle_api_response(self, response: requests.Response) -> Dict[str, Any]:
        """Handle API response with comprehensive error checking"""
        try:
            response.raise_for_status()
            data = response.json()
            health_monitor.record_api_call(True)
            return data
        except requests.exceptions.HTTPError as e:
            health_monitor.record_api_call(False)
            if response.status_code == 429:
                raise RateLimitExceeded(f"Rate limit exceeded for {self.name}")
            else:
                raise DataSourceError(f"HTTP error {response.status_code}: {e}")
        except requests.exceptions.RequestException as e:
            health_monitor.record_api_call(False)
            raise DataSourceError(f"Request failed for {self.name}: {e}")
        except json.JSONDecodeError as e:
            health_monitor.record_api_call(False)
            raise DataSourceError(f"Invalid JSON response from {self.name}: {e}")

class AlphaVantageSource(BaseDataSource):
    """Alpha Vantage data source with comprehensive error handling"""
    
    def __init__(self):
        rate_limiter = RateLimiter(calls_per_minute=5, calls_per_hour=500, calls_per_day=500)
        super().__init__("AlphaVantage", credentials.alpha_vantage_api_key, rate_limiter)
        self.base_url = "https://www.alphavantage.co/query"
    
    async def fetch_data(self, symbol: str, data_type: str = "TIME_SERIES_DAILY", **kwargs) -> MarketData:
        """Fetch data from Alpha Vantage"""
        try:
            self._wait_for_rate_limit()
            
            params = {
                'function': data_type,
                'symbol': symbol,
                'apikey': self.api_key,
                'outputsize': kwargs.get('outputsize', 'compact')
            }
            
            logger.info("Fetching Alpha Vantage data", symbol=symbol, function=data_type)
            
            response = self.session.get(self.base_url, params=params)
            data = self._handle_api_response(response)
            
            # Check for API error messages
            if 'Error Message' in data:
                raise DataSourceError(f"Alpha Vantage error: {data['Error Message']}")
            
            if 'Note' in data:
                raise RateLimitExceeded(f"Alpha Vantage rate limit: {data['Note']}")
            
            # Parse time series data
            parsed_data = self._parse_time_series_data(data, symbol)
            
            return MarketData(
                symbol=symbol,
                timestamp=datetime.now(),
                source=self.name,
                data_type='price',
                data=parsed_data,
                quality_score=self._calculate_quality_score(parsed_data),
                confidence=0.95
            )
            
        except Exception as e:
            logger.error("Alpha Vantage fetch failed", symbol=symbol, error=str(e))
            raise
    
    def _parse_time_series_data(self, data: Dict[str, Any], symbol: str) -> Dict[str, Any]:
        """Parse Alpha Vantage time series data"""
        # Find the time series key (varies by function)
        time_series_key = None
        for key in data.keys():
            if 'Time Series' in key:
                time_series_key = key
                break
        
        if not time_series_key:
            raise DataSourceError("No time series data found in Alpha Vantage response")
        
        time_series = data[time_series_key]
        
        # Convert to DataFrame
        df_data = []
        for date_str, values in time_series.items():
            row = {
                'date': pd.to_datetime(date_str),
                'open': float(values.get('1. open', 0)),
                'high': float(values.get('2. high', 0)),
                'low': float(values.get('3. low', 0)),
                'close': float(values.get('4. close', 0)),
                'volume': int(values.get('5. volume', 0))
            }
            df_data.append(row)
        
        df = pd.DataFrame(df_data)
        df.set_index('date', inplace=True)
        df.sort_index(inplace=True)
        
        # Validate data quality
        is_valid, quality_metrics = validator.validate_price_data(df)
        if not is_valid:
            logger.warning("Data quality issues detected", 
                         symbol=symbol, 
                         issues=quality_metrics['issues'])
        
        return {
            'price_data': df.to_dict('index'),
            'metadata': {
                'symbol': symbol,
                'source': self.name,
                'last_refreshed': data.get('Meta Data', {}).get('3. Last Refreshed'),
                'data_points': len(df),
                'quality_metrics': quality_metrics
            }
        }
    
    def _calculate_quality_score(self, parsed_data: Dict[str, Any]) -> float:
        """Calculate data quality score"""
        quality_metrics = parsed_data.get('metadata', {}).get('quality_metrics', {})
        issues = quality_metrics.get('issues', [])
        
        # Start with perfect score and deduct for issues
        score = 1.0
        for issue in issues:
            if 'missing' in issue.lower():
                score -= 0.2
            elif 'extreme' in issue.lower():
                score -= 0.1
            elif 'negative' in issue.lower():
                score -= 0.3
            else:
                score -= 0.05
        
        return max(0.0, score)

class FinnhubSource(BaseDataSource):
    """Finnhub data source for real-time market data"""
    
    def __init__(self):
        rate_limiter = RateLimiter(calls_per_minute=60, calls_per_hour=1000, calls_per_day=10000)
        super().__init__("Finnhub", credentials.finnhub_api_key, rate_limiter)
        self.base_url = "https://finnhub.io/api/v1"
    
    async def fetch_data(self, symbol: str, data_type: str = "quote", **kwargs) -> MarketData:
        """Fetch data from Finnhub"""
        try:
            self._wait_for_rate_limit()
            
            if data_type == "quote":
                endpoint = f"{self.base_url}/quote"
                params = {'symbol': symbol, 'token': self.api_key}
            elif data_type == "company":
                endpoint = f"{self.base_url}/stock/profile2"
                params = {'symbol': symbol, 'token': self.api_key}
            elif data_type == "news":
                endpoint = f"{self.base_url}/company-news"
                params = {
                    'symbol': symbol,
                    'token': self.api_key,
                    'from': kwargs.get('from_date', (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')),
                    'to': kwargs.get('to_date', datetime.now().strftime('%Y-%m-%d'))
                }
            else:
                raise ValueError(f"Unsupported data type: {data_type}")
            
            logger.info("Fetching Finnhub data", symbol=symbol, data_type=data_type)
            
            response = self.session.get(endpoint, params=params)
            data = self._handle_api_response(response)
            
            parsed_data = self._parse_finnhub_data(data, symbol, data_type)
            
            return MarketData(
                symbol=symbol,
                timestamp=datetime.now(),
                source=self.name,
                data_type=data_type,
                data=parsed_data,
                quality_score=self._calculate_finnhub_quality_score(data, data_type),
                confidence=0.90
            )
            
        except Exception as e:
            logger.error("Finnhub fetch failed", symbol=symbol, error=str(e))
            raise
    
    def _parse_finnhub_data(self, data: Dict[str, Any], symbol: str, data_type: str) -> Dict[str, Any]:
        """Parse Finnhub data based on type"""
        if data_type == "quote":
            return {
                'current_price': data.get('c', 0),
                'change': data.get('d', 0),
                'percent_change': data.get('dp', 0),
                'high': data.get('h', 0),
                'low': data.get('l', 0),
                'open': data.get('o', 0),
                'previous_close': data.get('pc', 0),
                'timestamp': datetime.fromtimestamp(data.get('t', 0)) if data.get('t') else datetime.now()
            }
        elif data_type == "company":
            return {
                'name': data.get('name', ''),
                'country': data.get('country', ''),
                'currency': data.get('currency', ''),
                'exchange': data.get('exchange', ''),
                'ipo': data.get('ipo', ''),
                'market_cap': data.get('marketCapitalization', 0),
                'shares_outstanding': data.get('shareOutstanding', 0),
                'industry': data.get('finnhubIndustry', ''),
                'logo': data.get('logo', ''),
                'phone': data.get('phone', ''),
                'weburl': data.get('weburl', '')
            }
        elif data_type == "news":
            parsed_news = []
            for article in data:
                parsed_news.append({
                    'category': article.get('category', ''),
                    'datetime': datetime.fromtimestamp(article.get('datetime', 0)),
                    'headline': article.get('headline', ''),
                    'source': article.get('source', ''),
                    'summary': article.get('summary', ''),
                    'url': article.get('url', '')
                })
            return {'news_articles': parsed_news}
        
        return data
    
    def _calculate_finnhub_quality_score(self, data: Dict[str, Any], data_type: str) -> float:
        """Calculate quality score for Finnhub data"""
        if data_type == "quote":
            # Check if we have valid price data
            required_fields = ['c', 'o', 'h', 'l']
            valid_fields = sum(1 for field in required_fields if data.get(field, 0) > 0)
            return valid_fields / len(required_fields)
        elif data_type == "company":
            # Check completeness of company data
            important_fields = ['name', 'country', 'currency', 'exchange', 'marketCapitalization']
            valid_fields = sum(1 for field in important_fields if data.get(field))
            return valid_fields / len(important_fields)
        elif data_type == "news":
            # Check if we have news articles
            return 1.0 if len(data) > 0 else 0.0
        
        return 0.5  # Default score

class FREDSource(BaseDataSource):
    """Federal Reserve Economic Data source"""
    
    def __init__(self):
        rate_limiter = RateLimiter(calls_per_minute=120, calls_per_hour=1000, calls_per_day=10000)
        super().__init__("FRED", credentials.fred_api_key, rate_limiter)
        self.base_url = "https://api.stlouisfed.org/fred"
    
    async def fetch_data(self, series_id: str, **kwargs) -> MarketData:
        """Fetch economic data from FRED"""
        try:
            self._wait_for_rate_limit()
            
            # Get series observations
            endpoint = f"{self.base_url}/series/observations"
            params = {
                'series_id': series_id,
                'api_key': self.api_key,
                'file_type': 'json',
                'limit': kwargs.get('limit', 1000),
                'sort_order': 'desc'
            }
            
            if 'start_date' in kwargs:
                params['observation_start'] = kwargs['start_date']
            if 'end_date' in kwargs:
                params['observation_end'] = kwargs['end_date']
            
            logger.info("Fetching FRED data", series_id=series_id)
            
            response = self.session.get(endpoint, params=params)
            data = self._handle_api_response(response)
            
            parsed_data = self._parse_fred_data(data, series_id)
            
            return MarketData(
                symbol=series_id,
                timestamp=datetime.now(),
                source=self.name,
                data_type='macro',
                data=parsed_data,
                quality_score=self._calculate_fred_quality_score(data),
                confidence=0.98
            )
            
        except Exception as e:
            logger.error("FRED fetch failed", series_id=series_id, error=str(e))
            raise
    
    def _parse_fred_data(self, data: Dict[str, Any], series_id: str) -> Dict[str, Any]:
        """Parse FRED data"""
        observations = data.get('observations', [])
        
        # Convert to DataFrame
        df_data = []
        for obs in observations:
            if obs['value'] != '.':  # FRED uses '.' for missing values
                df_data.append({
                    'date': pd.to_datetime(obs['date']),
                    'value': float(obs['value'])
                })
        
        df = pd.DataFrame(df_data)
        if not df.empty:
            df.set_index('date', inplace=True)
            df.sort_index(inplace=True)
        
        return {
            'series_data': df.to_dict('index') if not df.empty else {},
            'metadata': {
                'series_id': series_id,
                'source': self.name,
                'data_points': len(df),
                'latest_value': df['value'].iloc[-1] if not df.empty else None,
                'latest_date': df.index[-1].strftime('%Y-%m-%d') if not df.empty else None
            }
        }
    
    def _calculate_fred_quality_score(self, data: Dict[str, Any]) -> float:
        """Calculate quality score for FRED data"""
        observations = data.get('observations', [])
        if not observations:
            return 0.0
        
        # Calculate completeness (non-missing values)
        valid_values = sum(1 for obs in observations if obs['value'] != '.')
        completeness = valid_values / len(observations) if observations else 0
        
        return completeness

# Data source registry
class DataSourceRegistry:
    """Registry for managing data sources"""
    
    def __init__(self):
        self.sources = {}
        self._initialize_sources()
    
    def _initialize_sources(self):
        """Initialize all available data sources"""
        try:
            if credentials.alpha_vantage_api_key:
                self.sources['alpha_vantage'] = AlphaVantageSource()
                logger.info("Alpha Vantage source initialized")
        except Exception as e:
            logger.error("Failed to initialize Alpha Vantage", error=str(e))
        
        try:
            if credentials.finnhub_api_key:
                self.sources['finnhub'] = FinnhubSource()
                logger.info("Finnhub source initialized")
        except Exception as e:
            logger.error("Failed to initialize Finnhub", error=str(e))
        
        try:
            if credentials.fred_api_key:
                self.sources['fred'] = FREDSource()
                logger.info("FRED source initialized")
        except Exception as e:
            logger.error("Failed to initialize FRED", error=str(e))
    
    def get_source(self, name: str) -> Optional[BaseDataSource]:
        """Get a data source by name"""
        return self.sources.get(name)
    
    def list_sources(self) -> List[str]:
        """List all available data sources"""
        return list(self.sources.keys())
    
    async def fetch_multi_source_data(self, symbol: str, sources: List[str] = None) -> Dict[str, MarketData]:
        """Fetch data from multiple sources concurrently"""
        if sources is None:
            sources = self.list_sources()
        
        tasks = []
        for source_name in sources:
            source = self.get_source(source_name)
            if source:
                if source_name == 'fred':
                    # FRED needs series ID instead of symbol
                    continue
                tasks.append(self._fetch_with_timeout(source, symbol))
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        data = {}
        for i, result in enumerate(results):
            source_name = sources[i]
            if isinstance(result, Exception):
                logger.error("Source fetch failed", source=source_name, error=str(result))
            else:
                data[source_name] = result
        
        return data
    
    async def _fetch_with_timeout(self, source: BaseDataSource, symbol: str, timeout: int = 30):
        """Fetch data with timeout"""
        try:
            return await asyncio.wait_for(source.fetch_data(symbol), timeout=timeout)
        except asyncio.TimeoutError:
            raise DataSourceError(f"Timeout fetching data from {source.name} for {symbol}")

# Initialize global registry
data_registry = DataSourceRegistry()

logger.info("Data sources module initialized", 
           available_sources=data_registry.list_sources())
