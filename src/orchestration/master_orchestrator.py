"""
Master Orchestrator - Elite Market Analysis System
99.9th percentile production-ready orchestration with Bayesian synthesis
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass, field
import structlog
from scipy import stats, optimize
from scipy.special import softmax
import json
import warnings
warnings.filterwarnings('ignore')

from ..agents.base import BaseAgent, AgentResult, AgentError
from ..agents.macroeconomic import MacroeconomicAgent
from ..agents.technical import EnhancedTechnicalAnalysisAgent
from ..agents.enhanced_quantitative import EnhancedQuantitativeAgent
from ..agents.sector import SectorAnalysisAgent
from ..analysis.sentiment import CompositeSentimentAnalyzer
from ..core.system import health_monitor

logger = structlog.get_logger()

@dataclass
class ScenarioForecast:
    """Structured scenario forecast with confidence intervals"""
    scenario_name: str
    probability: float
    price_forecast: Dict[str, float]  # symbol -> predicted price
    confidence_interval: Dict[str, <PERSON><PERSON>[float, float]]  # symbol -> (lower, upper)
    risk_metrics: Dict[str, float]
    supporting_factors: List[str]
    time_horizon: int  # days

@dataclass
class IntegratedForecast:
    """Master integrated forecast output"""
    timestamp: datetime
    symbols: List[str]
    base_scenario: ScenarioForecast
    bear_scenario: ScenarioForecast
    bull_scenario: ScenarioForecast
    consensus_forecast: Dict[str, float]
    confidence_scores: Dict[str, float]
    black_swan_risks: List[Dict[str, Any]]
    agent_contributions: Dict[str, float]
    portfolio_recommendations: Dict[str, Any]
    metadata: Dict[str, Any]

class BayesianSynthesizer:
    """Bayesian model combination and uncertainty quantification"""
    
    def __init__(self):
        self.agent_priors = {
            'quantitative': 0.40,
            'macroeconomic': 0.25,
            'technical_analysis': 0.15,
            'sector': 0.10,
            'sentiment': 0.10
        }
        self.learning_rate = 0.02
        self.historical_performance = {}
    
    def update_agent_weights(self, performance_metrics: Dict[str, Dict[str, float]]):
        """Update agent weights based on historical performance"""
        for agent_name, metrics in performance_metrics.items():
            if agent_name in self.agent_priors:
                accuracy = metrics.get('accuracy', 0.5)
                confidence = metrics.get('average_confidence', 0.5)
                
                # Bayesian update
                performance_score = (accuracy * 0.7 + confidence * 0.3)
                current_weight = self.agent_priors[agent_name]
                
                # Exponential moving average update
                self.agent_priors[agent_name] = (
                    (1 - self.learning_rate) * current_weight + 
                    self.learning_rate * performance_score
                )
        
        # Normalize weights
        total_weight = sum(self.agent_priors.values())
        if total_weight > 0:
            for agent in self.agent_priors:
                self.agent_priors[agent] /= total_weight
    
    def synthesize_forecasts(self, agent_results: Dict[str, AgentResult]) -> Dict[str, Any]:
        """Bayesian synthesis of agent forecasts"""
        try:
            consensus = {}
            uncertainty_estimates = {}
            
            # Get symbols from any agent result
            symbols = next(iter(agent_results.values())).symbols if agent_results else []
            
            for symbol in symbols:
                forecasts = []
                weights = []
                confidences = []
                
                for agent_name, result in agent_results.items():
                    if agent_name in self.agent_priors and result.confidence > 0.3:
                        # Extract price forecast from agent result
                        price_forecast = self._extract_price_forecast(result, symbol)
                        if price_forecast is not None:
                            forecasts.append(price_forecast)
                            weights.append(self.agent_priors[agent_name] * result.confidence)
                            confidences.append(result.confidence)
                
                if forecasts:
                    # Weighted average with uncertainty
                    weights = np.array(weights)
                    weights = weights / weights.sum() if weights.sum() > 0 else weights
                    
                    consensus_forecast = np.average(forecasts, weights=weights)
                    
                    # Calculate uncertainty (higher when agents disagree)
                    forecast_std = np.sqrt(np.average((forecasts - consensus_forecast)**2, weights=weights))
                    avg_confidence = np.average(confidences, weights=weights)
                    
                    consensus[symbol] = consensus_forecast
                    uncertainty_estimates[symbol] = {
                        'forecast_std': forecast_std,
                        'confidence': avg_confidence,
                        'agent_agreement': 1.0 - (forecast_std / consensus_forecast) if consensus_forecast != 0 else 0.5
                    }
            
            return {
                'consensus_forecasts': consensus,
                'uncertainty_estimates': uncertainty_estimates,
                'agent_weights': self.agent_priors.copy()
            }
            
        except Exception as e:
            logger.error("Bayesian synthesis failed", error=str(e))
            return {
                'consensus_forecasts': {},
                'uncertainty_estimates': {},
                'agent_weights': self.agent_priors.copy()
            }
    
    def _extract_price_forecast(self, result: AgentResult, symbol: str) -> Optional[float]:
        """Extract price forecast from agent result"""
        try:
            predictions = result.predictions
            
            # Different agents structure predictions differently
            if result.agent_name == 'quantitative':
                forecasts = predictions.get('price_forecasts', {})
                return forecasts.get(symbol, {}).get('expected_return', 0.0)
            
            elif result.agent_name == 'technical_analysis':
                signals = predictions.get('trading_signals', {}).get(symbol, {})
                combined_signal = signals.get('combined_signal', {})
                signal_strength = combined_signal.get('strength', 0.0)
                direction = combined_signal.get('direction', 'hold')
                
                # Convert technical signal to price forecast
                if direction == 'buy':
                    return signal_strength * 0.1  # 10% max positive forecast
                elif direction == 'sell':
                    return -signal_strength * 0.1  # 10% max negative forecast
                else:
                    return 0.0
            
            elif result.agent_name == 'macroeconomic':
                symbol_impacts = predictions.get('symbol_impacts', {}).get(symbol, {})
                return symbol_impacts.get('impact_score', 0.0)
            
            elif result.agent_name == 'sentiment':
                composite = predictions.get('composite_sentiment', {})
                return composite.get('score', 0.0) * 0.05  # 5% max impact
            
            elif result.agent_name == 'sector':
                analysis = predictions.get('sector_analysis', {}).get(symbol, {})
                return analysis.get('outlook_score', 0.0)
            
            return 0.0
            
        except Exception as e:
            logger.warning("Failed to extract price forecast", agent=result.agent_name, symbol=symbol, error=str(e))
            return None

class BlackSwanDetector:
    """Advanced black swan risk detection and quantification"""
    
    def __init__(self):
        self.tail_threshold = 0.005  # 0.5% probability threshold
        self.impact_threshold = 0.20  # 20% impact threshold
    
    def detect_black_swan_risks(self, agent_results: Dict[str, AgentResult], 
                               market_conditions: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Detect potential black swan events"""
        risks = []
        
        # Provide default market conditions if not supplied
        if market_conditions is None:
            market_conditions = {
                'volatility': 1.0,
                'stress_indicator': 1.0,
                'regime': 'normal'
            }
        
        try:
            # Collect risk factors from all agents
            all_risk_factors = []
            for result in agent_results.values():
                all_risk_factors.extend(result.risk_factors)
            
            # Analyze correlation breakdowns
            correlation_risks = self._detect_correlation_breakdown(agent_results)
            risks.extend(correlation_risks)
            
            # Detect regime change risks
            regime_risks = self._detect_regime_change_risks(agent_results)
            risks.extend(regime_risks)
            
            # Analyze liquidity risks
            liquidity_risks = self._detect_liquidity_risks(agent_results)
            risks.extend(liquidity_risks)
            
            # Detect tail risks from quantitative analysis
            tail_risks = self._detect_tail_risks(agent_results)
            risks.extend(tail_risks)
            
            # Score and filter risks
            scored_risks = []
            for risk in risks:
                risk_score = self._score_black_swan_risk(risk, market_conditions)
                if risk_score > 0.5:  # Only include significant risks
                    risk['risk_score'] = risk_score
                    scored_risks.append(risk)
            
            # Sort by risk score
            scored_risks.sort(key=lambda x: x['risk_score'], reverse=True)
            
            return scored_risks[:10]  # Top 10 risks
            
        except Exception as e:
            logger.error("Black swan detection failed", error=str(e))
            return [{'type': 'system_risk', 'description': 'Risk detection system failure', 'probability': 0.01, 'impact': 0.1}]
    
    def _detect_correlation_breakdown(self, agent_results: Dict[str, AgentResult]) -> List[Dict[str, Any]]:
        """Detect potential correlation breakdown scenarios"""
        risks = []
        
        # Look for conflicting signals between agents
        agent_directions = {}
        for agent_name, result in agent_results.items():
            if hasattr(result.predictions, 'get'):
                # Extract general directional bias
                if 'trading_signals' in result.predictions:
                    signals = result.predictions['trading_signals']
                    buy_signals = sum(1 for s in signals.values() if s.get('combined_signal', {}).get('direction') == 'buy')
                    sell_signals = sum(1 for s in signals.values() if s.get('combined_signal', {}).get('direction') == 'sell')
                    agent_directions[agent_name] = 1 if buy_signals > sell_signals else -1 if sell_signals > buy_signals else 0
        
        # Check for strong disagreement
        if len(set(agent_directions.values())) >= 3:  # High disagreement
            risks.append({
                'type': 'correlation_breakdown',
                'description': 'Strong disagreement between analysis methods suggests potential correlation breakdown',
                'probability': 0.02,
                'impact': 0.25,
                'affected_factors': list(agent_directions.keys())
            })
        
        return risks
    
    def _detect_regime_change_risks(self, agent_results: Dict[str, AgentResult]) -> List[Dict[str, Any]]:
        """Detect regime change risks"""
        risks = []
        
        # Check macro regime analysis
        for result in agent_results.values():
            if result.agent_name == 'macroeconomic':
                regime = result.predictions.get('economic_regime', {})
                if regime.get('regime') == 'transition' or regime.get('strength', 1.0) < 0.6:
                    risks.append({
                        'type': 'regime_change',
                        'description': f"Economic regime transition detected: {regime.get('regime', 'unknown')}",
                        'probability': 0.03,
                        'impact': 0.30,
                        'regime_data': regime
                    })
        
        return risks
    
    def _detect_liquidity_risks(self, agent_results: Dict[str, AgentResult]) -> List[Dict[str, Any]]:
        """Detect liquidity risks"""
        risks = []
        
        # Check technical analysis for volume divergences
        for result in agent_results.values():
            if result.agent_name == 'technical_analysis':
                volume_analysis = result.predictions.get('volume_analysis', {})
                if not volume_analysis.get('volume_confirmation', True):
                    risks.append({
                        'type': 'liquidity_risk',
                        'description': 'Volume divergence suggests potential liquidity stress',
                        'probability': 0.015,
                        'impact': 0.20,
                        'volume_data': volume_analysis
                    })
        
        return risks
    
    def _detect_tail_risks(self, agent_results: Dict[str, AgentResult]) -> List[Dict[str, Any]]:
        """Detect tail risks from quantitative analysis"""
        risks = []
        
        for result in agent_results.values():
            if result.agent_name == 'quantitative':
                risk_metrics = result.predictions.get('risk_metrics', {})
                
                # Check VaR breaches
                var_99 = risk_metrics.get('var_99', 0)
                if abs(var_99) > 0.15:  # 15% VaR
                    risks.append({
                        'type': 'tail_risk',
                        'description': f'High tail risk detected: 99% VaR = {var_99:.2%}',
                        'probability': 0.01,
                        'impact': abs(var_99),
                        'var_data': risk_metrics
                    })
        
        return risks
    
    def _detect_market_stress(self, agent_results: Dict[str, AgentResult]) -> Dict[str, Any]:
        """Detect market stress indicators"""
        try:
            stress_indicators = {
                'volatility_stress': 0.0,
                'correlation_stress': 0.0,
                'liquidity_stress': 0.0,
                'momentum_stress': 0.0,
                'overall_stress': 0.0
            }
            
            total_confidence = 0.0
            valid_results = 0
            
            for agent_name, result in agent_results.items():
                if result and hasattr(result, 'confidence'):
                    total_confidence += result.confidence
                    valid_results += 1
                    
                    # Check for volatility stress
                    if hasattr(result, 'risk_factors'):
                        for risk_factor in result.risk_factors:
                            if 'volatility' in risk_factor.lower():
                                stress_indicators['volatility_stress'] += 0.2
                            elif 'correlation' in risk_factor.lower():
                                stress_indicators['correlation_stress'] += 0.2
                            elif 'liquidity' in risk_factor.lower():
                                stress_indicators['liquidity_stress'] += 0.2
                            elif 'momentum' in risk_factor.lower():
                                stress_indicators['momentum_stress'] += 0.2
            
            # Calculate overall stress based on average confidence
            if valid_results > 0:
                avg_confidence = total_confidence / valid_results
                # Lower confidence indicates higher stress
                stress_indicators['overall_stress'] = max(0.0, (70.0 - avg_confidence) / 70.0)
            
            # Cap individual stress indicators at 1.0
            for key in stress_indicators:
                stress_indicators[key] = min(1.0, stress_indicators[key])
            
            return stress_indicators
            
        except Exception as e:
            logger.error("Market stress detection failed", error=str(e))
            return {
                'volatility_stress': 0.5,
                'correlation_stress': 0.5,
                'liquidity_stress': 0.5,
                'momentum_stress': 0.5,
                'overall_stress': 0.5
            }
    
    def _score_black_swan_risk(self, risk: Dict[str, Any], market_conditions: Dict[str, Any]) -> float:
        """Score black swan risk based on probability and impact"""
        probability = risk.get('probability', 0.01)
        impact = risk.get('impact', 0.1)
        
        # Base score
        base_score = probability * impact * 100  # Scale to 0-1
        
        # Adjust based on market conditions
        volatility_multiplier = market_conditions.get('volatility', 1.0)
        stress_multiplier = market_conditions.get('stress_indicator', 1.0)
        
        adjusted_score = base_score * volatility_multiplier * stress_multiplier
        
        return min(1.0, adjusted_score)

class NoiseManagementSystem:
    """Advanced noise filtering for reliable stock forecasting"""
    
    def __init__(self):
        self.noise_thresholds = {
            'volume_anomaly': 3.0,  # Standard deviations
            'price_spike': 2.5,
            'correlation_break': 0.3,
            'volatility_regime': 2.0
        }
        self.filters = {
            'outlier_detection': True,
            'regime_filtering': True,
            'correlation_validation': True,
            'volume_confirmation': True
        }
    
    def filter_market_noise(self, data: pd.DataFrame, symbol: str) -> Dict[str, Any]:
        """Apply comprehensive noise filtering to market data"""
        try:
            filtered_data = data.copy()
            noise_flags = {}
            
            # 1. Volume anomaly detection
            volume_zscore = self._calculate_volume_zscore(filtered_data)
            volume_outliers = abs(volume_zscore) > self.noise_thresholds['volume_anomaly']
            noise_flags['volume_anomalies'] = volume_outliers.sum()
            
            # 2. Price spike detection
            returns = filtered_data['close'].pct_change()
            return_zscore = (returns - returns.mean()) / returns.std()
            price_spikes = abs(return_zscore) > self.noise_thresholds['price_spike']
            noise_flags['price_spikes'] = price_spikes.sum()
            
            # 3. Volatility regime detection
            rolling_vol = returns.rolling(window=20).std()
            vol_zscore = (rolling_vol - rolling_vol.mean()) / rolling_vol.std()
            vol_regimes = abs(vol_zscore) > self.noise_thresholds['volatility_regime']
            noise_flags['volatility_regimes'] = vol_regimes.sum()
            
            # 4. Apply filters
            if self.filters['outlier_detection']:
                # Remove extreme outliers but keep data structure
                outlier_mask = price_spikes | volume_outliers
                filtered_data.loc[outlier_mask, 'noise_flag'] = True
            
            # Calculate noise score
            total_observations = len(filtered_data)
            total_noise = sum(noise_flags.values())
            noise_score = total_noise / total_observations if total_observations > 0 else 0
            
            return {
                'filtered_data': filtered_data,
                'noise_metrics': noise_flags,
                'noise_score': noise_score,
                'data_quality': 1.0 - noise_score,
                'usable_observations': total_observations - total_noise
            }
            
        except Exception as e:
            logger.error("Noise filtering failed", symbol=symbol, error=str(e))
            return {
                'filtered_data': data,
                'noise_metrics': {},
                'noise_score': 0.5,
                'data_quality': 0.5,
                'usable_observations': len(data)
            }
    
    def _calculate_volume_zscore(self, data: pd.DataFrame) -> pd.Series:
        """Calculate volume z-score for anomaly detection"""
        if 'volume' not in data.columns:
            return pd.Series(0, index=data.index)
        
        volume = data['volume']
        rolling_mean = volume.rolling(window=20).mean()
        rolling_std = volume.rolling(window=20).std()
        
        return (volume - rolling_mean) / rolling_std

class StockForecastingEngine:
    """Specialized engine for stock price forecasting with reliability focus"""
    
    def __init__(self):
        self.models = {
            'technical_momentum': 0.25,
            'mean_reversion': 0.25, 
            'fundamental_scoring': 0.30,
            'volatility_modeling': 0.20
        }
        self.timeframes = {
            '1d': 1,
            '5d': 5,
            '21d': 21
        }
    
    def generate_stock_forecast(self, symbol: str, market_data: pd.DataFrame, 
                              fundamental_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Generate multi-timeframe stock forecast with confidence intervals"""
        try:
            forecasts = {}
            
            # Generate forecasts for each timeframe
            for timeframe, days in self.timeframes.items():
                forecast = self._generate_timeframe_forecast(
                    symbol, market_data, days, fundamental_data
                )
                forecasts[timeframe] = forecast
            
            # Calculate consensus forecast
            consensus = self._calculate_consensus_forecast(forecasts)
            
            # Generate confidence intervals
            confidence_intervals = self._calculate_confidence_intervals(
                forecasts, market_data
            )
            
            return {
                'symbol': symbol,
                'forecasts_by_timeframe': forecasts,
                'consensus_forecast': consensus,
                'confidence_intervals': confidence_intervals,
                'forecast_quality': self._assess_forecast_quality(forecasts),
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            logger.error("Stock forecasting failed", symbol=symbol, error=str(e))
            return self._create_default_forecast(symbol)
    
    def _generate_timeframe_forecast(self, symbol: str, data: pd.DataFrame, 
                                   days: int, fundamental_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate forecast for specific timeframe with enhanced confidence"""
        try:
            current_price = data['close'].iloc[-1]
            
            # Technical momentum signal
            momentum_signal = self._calculate_momentum_forecast(data, days)
            
            # Mean reversion signal  
            reversion_signal = self._calculate_reversion_forecast(data, days)
            
            # Fundamental signal (if available)
            fundamental_signal = self._calculate_fundamental_forecast(
                fundamental_data, days
            ) if fundamental_data else 0.0
            
            # Volatility adjustment
            volatility_adj = self._calculate_volatility_adjustment(data, days)
            
            # Enhanced: Volume confirmation factor
            volume_conf = self._calculate_volume_confirmation(data, days)
            
            # Enhanced: Trend strength measurement
            trend_strength = self._calculate_trend_strength(data, days)
            
            # Combine signals with model weights
            weighted_forecast = (
                momentum_signal * self.models['technical_momentum'] +
                reversion_signal * self.models['mean_reversion'] +
                fundamental_signal * self.models['fundamental_scoring']
            )
            
            # Apply volatility adjustment
            adjusted_forecast = weighted_forecast * volatility_adj
            
            # Calculate target price
            target_price = current_price * (1 + adjusted_forecast)
            
            # Enhanced confidence calculation
            base_confidence = self._calculate_signal_confidence(
                momentum_signal, reversion_signal, fundamental_signal
            )
            
            # Apply volume and trend adjustments to confidence
            volume_weighted_conf = base_confidence * (0.7 + 0.3 * volume_conf)
            final_confidence = volume_weighted_conf * (0.8 + 0.2 * trend_strength)
            
            return {
                'target_price': target_price,
                'expected_return': adjusted_forecast,
                'signal_components': {
                    'momentum': momentum_signal,
                    'reversion': reversion_signal,
                    'fundamental': fundamental_signal,
                    'volatility_adj': volatility_adj,
                    'volume_confirmation': volume_conf,
                    'trend_strength': trend_strength
                },
                'confidence': np.clip(final_confidence, 0.35, 0.90)
            }
            
        except Exception as e:
            logger.error("Timeframe forecast failed", 
                       symbol=symbol, timeframe=days, error=str(e))
            return {
                'target_price': data['close'].iloc[-1],
                'expected_return': 0.0,
                'signal_components': {},
                'confidence': 0.3
            }
    
    def _calculate_momentum_forecast(self, data: pd.DataFrame, days: int) -> float:
        """Calculate momentum-based forecast"""
        try:
            # Multiple momentum indicators
            returns = data['close'].pct_change()
            
            # Short-term momentum (5-day)
            short_momentum = returns.rolling(window=5).mean().iloc[-1]
            
            # Medium-term momentum (20-day) 
            medium_momentum = returns.rolling(window=20).mean().iloc[-1]
            
            # RSI momentum
            rsi = self._calculate_rsi(data['close'])
            rsi_signal = (50 - rsi) / 100  # Convert to signal
            
            # Combine momentum signals
            momentum_forecast = (
                short_momentum * 0.4 +
                medium_momentum * 0.4 +
                rsi_signal * 0.2
            )
            
            # Scale by timeframe
            timeframe_multiplier = np.sqrt(days / 21)  # 21-day baseline
            
            return momentum_forecast * timeframe_multiplier
            
        except Exception:
            return 0.0
    
    def _calculate_reversion_forecast(self, data: pd.DataFrame, days: int) -> float:
        """Calculate mean reversion forecast"""
        try:
            prices = data['close']
            
            # Calculate deviation from moving averages
            ma_20 = prices.rolling(window=20).mean().iloc[-1]
            ma_50 = prices.rolling(window=50).mean().iloc[-1]
            current_price = prices.iloc[-1]
            
            # Short-term reversion signal
            short_reversion = (ma_20 - current_price) / current_price
            
            # Long-term reversion signal
            long_reversion = (ma_50 - current_price) / current_price
            
            # Bollinger Band position
            bb_upper, bb_lower = self._calculate_bollinger_bands(prices)
            bb_position = (current_price - bb_lower) / (bb_upper - bb_lower)
            bb_signal = (0.5 - bb_position) * 0.1  # Max 5% signal
            
            # Combine reversion signals
            reversion_forecast = (
                short_reversion * 0.4 +
                long_reversion * 0.4 + 
                bb_signal * 0.2
            )
            
            # Scale by timeframe (reversion works better short-term)
            timeframe_multiplier = np.sqrt(21 / max(days, 1))
            
            return reversion_forecast * timeframe_multiplier
            
        except Exception:
            return 0.0
    
    def _calculate_fundamental_forecast(self, fundamental_data: Dict[str, Any], days: int) -> float:
        """Calculate fundamental-based forecast (placeholder)"""
        # This would incorporate P/E ratios, earnings growth, etc.
        # For now, return neutral signal
        return 0.0
    
    def _calculate_volatility_adjustment(self, data: pd.DataFrame, days: int) -> float:
        """Calculate volatility-based forecast adjustment"""
        try:
            returns = data['close'].pct_change()
            volatility = returns.rolling(window=20).std().iloc[-1]
            
            # Higher volatility = lower confidence = smaller signals
            vol_adjustment = 1.0 / (1.0 + volatility * 10)  # Scale down high vol
            
            return np.clip(vol_adjustment, 0.3, 1.0)  # Keep reasonable bounds
            
        except Exception:
            return 0.7  # Default moderate adjustment
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> float:
        """Calculate RSI indicator"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi.iloc[-1]
        except Exception:
            return 50.0  # Neutral RSI
    
    def _calculate_bollinger_bands(self, prices: pd.Series, period: int = 20, std_dev: int = 2) -> Tuple[float, float]:
        """Calculate Bollinger Bands"""
        try:
            sma = prices.rolling(window=period).mean().iloc[-1]
            std = prices.rolling(window=period).std().iloc[-1]
            upper = sma + (std * std_dev)
            lower = sma - (std * std_dev)
            return upper, lower
        except Exception:
            current = prices.iloc[-1]
            return current * 1.05, current * 0.95  # 5% bands as fallback
    
    def _calculate_signal_confidence(self, momentum: float, reversion: float, fundamental: float) -> float:
        """Enhanced confidence calculation with signal normalization and trend analysis"""
        try:
            # Handle case where we don't have enough data for normalization
            if not hasattr(self, '_signal_history'):
                # Fallback to improved basic calculation
                signals = [momentum, reversion, fundamental]
                # Remove extreme outliers that distort confidence
                signals = [s for s in signals if abs(s) < 0.5]  # Remove signals > 50%
                if len(signals) < 2:
                    return 0.35  # Conservative default
                
                signal_agreement = 1.0 - np.std(signals) / (np.mean(np.abs(signals)) + 0.05)
                return np.clip(signal_agreement, 0.35, 0.85)
            
            # Enhanced calculation with normalization
            raw_signals = [momentum, reversion, fundamental]
            
            # Normalize signals to comparable scales using tanh to bound extreme values
            normalized_signals = []
            for signal in raw_signals:
                # Use tanh to compress extreme values while preserving direction
                normalized = np.tanh(signal * 10)  # Scale up then compress
                normalized_signals.append(normalized)
            
            # Calculate directional agreement (all signals pointing same way)
            positive_signals = sum(1 for s in normalized_signals if s > 0.1)
            negative_signals = sum(1 for s in normalized_signals if s < -0.1)
            neutral_signals = len(normalized_signals) - positive_signals - negative_signals
            
            # Directional consensus bonus
            if positive_signals >= 2 or negative_signals >= 2:
                directional_bonus = 0.15  # Bonus for agreement
            else:
                directional_bonus = 0.0
            
            # Signal strength (stronger signals = higher confidence)
            avg_strength = np.mean(np.abs(normalized_signals))
            strength_factor = min(avg_strength * 2, 1.0)  # Cap at 1.0
            
            # Base agreement calculation
            signal_agreement = 1.0 - np.std(normalized_signals) / (np.mean(np.abs(normalized_signals)) + 0.1)
            
            # Combine factors
            enhanced_confidence = (
                signal_agreement * 0.5 +           # 50% signal agreement
                strength_factor * 0.3 +            # 30% signal strength  
                directional_bonus                   # 15% directional bonus
            )
            
            # Apply realistic bounds (35% to 85% confidence range)
            return np.clip(enhanced_confidence, 0.35, 0.85)
            
        except Exception as e:
            # Robust fallback
            return 0.40
    
    def _calculate_consensus_forecast(self, forecasts: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate consensus across timeframes"""
        try:
            expected_returns = []
            confidences = []
            
            for timeframe, forecast in forecasts.items():
                expected_returns.append(forecast['expected_return'])
                confidences.append(forecast['confidence'])
            
            # Weight by confidence
            weights = np.array(confidences)
            weights = weights / weights.sum() if weights.sum() > 0 else np.ones_like(weights) / len(weights)
            
            consensus_return = np.average(expected_returns, weights=weights)
            consensus_confidence = np.mean(confidences)
            
            return {
                'expected_return': consensus_return,
                'confidence': consensus_confidence,
                'signal_strength': abs(consensus_return) * consensus_confidence
            }
            
        except Exception:
            return {
                'expected_return': 0.0,
                'confidence': 0.5,
                'signal_strength': 0.0
            }
    
    def _calculate_confidence_intervals(self, forecasts: Dict[str, Dict[str, Any]], 
                                      data: pd.DataFrame) -> Dict[str, Tuple[float, float]]:
        """Calculate confidence intervals for forecasts"""
        try:
            intervals = {}
            current_price = data['close'].iloc[-1]
            
            # Historical volatility for interval calculation
            returns = data['close'].pct_change()
            volatility = returns.std()
            
            for timeframe, forecast in forecasts.items():
                expected_return = forecast['expected_return']
                confidence = forecast['confidence']
                
                # Adjust interval width based on confidence and volatility
                interval_width = volatility * (2.0 - confidence) * 1.96  # 95% interval
                
                target_price = current_price * (1 + expected_return)
                lower_bound = target_price * (1 - interval_width)
                upper_bound = target_price * (1 + interval_width)
                
                intervals[timeframe] = (lower_bound, upper_bound)
            
            return intervals
            
        except Exception:
            current_price = data['close'].iloc[-1]
            default_interval = (current_price * 0.9, current_price * 1.1)
            return {tf: default_interval for tf in forecasts.keys()}
    
    def _assess_forecast_quality(self, forecasts: Dict[str, Dict[str, Any]]) -> float:
        """Assess overall forecast quality"""
        try:
            confidences = [f['confidence'] for f in forecasts.values()]
            signal_strengths = [abs(f['expected_return']) for f in forecasts.values()]
            
            avg_confidence = np.mean(confidences)
            avg_signal_strength = np.mean(signal_strengths)
            
            # Quality is combination of confidence and signal strength
            quality = (avg_confidence * 0.7 + min(avg_signal_strength * 10, 1.0) * 0.3)
            
            return np.clip(quality, 0.0, 1.0)
            
        except Exception:
            return 0.5
    
    def _calculate_volume_confirmation(self, data: pd.DataFrame, days: int) -> float:
        """Calculate volume confirmation factor for enhanced confidence"""
        try:
            if 'volume' not in data.columns:
                return 0.5  # Neutral if no volume data
            
            # Average volume over lookback period
            lookback = min(20, len(data))
            avg_volume = data['volume'].tail(lookback).mean()
            
            # Recent volume (last few days based on timeframe)
            recent_days = min(max(days // 4, 1), 5)  # Scale with timeframe
            recent_volume = data['volume'].tail(recent_days).mean()
            
            if avg_volume == 0:
                return 0.5
            
            # Volume ratio (recent vs average)
            volume_ratio = recent_volume / avg_volume
            
            # Convert to confidence factor (0.0 to 1.0)
            # Higher volume = higher confidence, but cap at 2x average
            volume_factor = min(volume_ratio, 2.0) / 2.0
            
            return np.clip(volume_factor, 0.2, 1.0)
            
        except Exception:
            return 0.5  # Neutral default
    
    def _calculate_trend_strength(self, data: pd.DataFrame, days: int) -> float:
        """Calculate trend strength using R-squared of price trend"""
        try:
            # Use appropriate lookback based on forecast timeframe
            lookback = min(max(days * 2, 10), 40)  # 2x forecast period, capped
            prices = data['close'].tail(lookback).values
            
            if len(prices) < 5:
                return 0.5  # Need minimum data
            
            # Linear regression of price trend
            x = np.arange(len(prices))
            slope, intercept, r_value, p_value, std_err = stats.linregress(x, prices)
            
            # R-squared shows how well prices fit the trend line
            r_squared = r_value ** 2
            
            # Strong trends (R² > 0.8) increase confidence
            # Weak trends (R² < 0.3) decrease confidence
            if r_squared > 0.8:
                trend_strength = 0.9 + 0.1 * (r_squared - 0.8) / 0.2  # 0.9-1.0
            elif r_squared > 0.5:
                trend_strength = 0.7 + 0.2 * (r_squared - 0.5) / 0.3  # 0.7-0.9
            else:
                trend_strength = 0.5 + 0.2 * r_squared / 0.5  # 0.5-0.7
            
            return np.clip(trend_strength, 0.3, 1.0)
            
        except Exception:
            return 0.6  # Slightly positive default
    
    def _create_default_forecast(self, symbol: str) -> Dict[str, Any]:
        """Create default forecast when analysis fails"""
        return {
            'symbol': symbol,
            'forecasts_by_timeframe': {
                '1d': {'target_price': 0, 'expected_return': 0.0, 'confidence': 0.3},
                '5d': {'target_price': 0, 'expected_return': 0.0, 'confidence': 0.3},
                '21d': {'target_price': 0, 'expected_return': 0.0, 'confidence': 0.3}
            },
            'consensus_forecast': {'expected_return': 0.0, 'confidence': 0.3, 'signal_strength': 0.0},
            'integrated_forecast': {'expected_return': 0.0, 'confidence': 0.3, 'signal_strength': 0.0},
            'confidence_intervals': {},
            'forecast_quality': 0.3,
            'data_quality_score': 0.3,
            'timestamp': datetime.now(),
            'status': 'fallback'
        }
    
    def generate_forecast(self, symbol: str, days: int = 5) -> Dict[str, Any]:
        """Generate forecast for a single symbol and timeframe"""
        try:
            # Get market data
            import yfinance as yf
            ticker = yf.Ticker(symbol)
            data = ticker.history(period="3mo")
            
            if data.empty:
                return {'error': f'No data available for {symbol}'}
            
            # Rename columns to lowercase for consistency
            data.columns = [col.lower() for col in data.columns]
            
            current_price = data['close'].iloc[-1]
            
            # Calculate basic forecast
            forecast_result = self._generate_timeframe_forecast(symbol, data, days, None)
            
            return {
                'symbol': symbol,
                'current_price': float(current_price),
                'target_price': forecast_result['target_price'],
                'expected_return': forecast_result['expected_return'],
                'confidence': forecast_result['confidence'],
                'signals': forecast_result['signal_components'],
                'timeframe_days': days,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {'error': f'Forecast generation failed: {str(e)}'}
    
    def calculate_portfolio_risk(self, portfolio: Dict[str, Dict[str, float]]) -> Dict[str, Any]:
        """Calculate portfolio risk metrics"""
        try:
            import yfinance as yf
            
            # Calculate portfolio value and get return data
            total_value = 0
            returns_data = {}
            weights = {}
            
            for symbol, position in portfolio.items():
                shares = position['shares']
                avg_cost = position['avg_cost']
                
                # Get current price
                ticker = yf.Ticker(symbol)
                current_data = ticker.history(period="1d")
                if current_data.empty:
                    continue
                    
                current_price = current_data['Close'].iloc[-1]
                position_value = shares * current_price
                total_value += position_value
                
                # Get historical returns
                hist_data = ticker.history(period="1y")
                if len(hist_data) > 20:
                    returns = hist_data['Close'].pct_change().dropna()
                    returns_data[symbol] = returns
                    weights[symbol] = position_value
            
            if not returns_data or total_value == 0:
                return {'error': 'Insufficient data for risk calculation'}
            
            # Normalize weights
            for symbol in weights:
                weights[symbol] = weights[symbol] / total_value
            
            # Calculate portfolio metrics
            portfolio_returns = []
            for date in returns_data[list(returns_data.keys())[0]].index:
                daily_return = 0
                for symbol, weight in weights.items():
                    if date in returns_data[symbol].index:
                        daily_return += weight * returns_data[symbol][date]
                portfolio_returns.append(daily_return)
            
            portfolio_returns = np.array(portfolio_returns)
            
            # Risk metrics
            annual_return = np.mean(portfolio_returns) * 252
            annual_vol = np.std(portfolio_returns) * np.sqrt(252)
            sharpe_ratio = annual_return / annual_vol if annual_vol > 0 else 0
            var_95 = np.percentile(portfolio_returns, 5) * np.sqrt(total_value)
            var_99 = np.percentile(portfolio_returns, 1) * np.sqrt(total_value)
            
            return {
                'total_value': float(total_value),
                'annual_return': float(annual_return),
                'annual_volatility': float(annual_vol),
                'sharpe_ratio': float(sharpe_ratio),
                'var_95': float(var_95),
                'var_99': float(var_99),
                'number_of_positions': len(portfolio),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {'error': f'Portfolio risk calculation failed: {str(e)}'}

class MasterOrchestrator:
    """Stock-focused forecasting system with noise management"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logger = structlog.get_logger().bind(component="stock_forecaster")
        
        # Simplified stock-focused components
        self.noise_filter = NoiseManagementSystem()
        self.stock_analyzer = StockForecastingEngine()
        self.bayesian_synthesizer = BayesianSynthesizer()
        self.black_swan_detector = BlackSwanDetector()
        
        # Initialize core agents (reduced complexity)
        self.agents = {}
        self._initialize_stock_agents()
        
        # Performance tracking with validation
        self.performance_history = []
        self.forecast_accuracy = {}
        self.noise_metrics = {}
        
        # Stock-focused configuration
        self.forecast_config = {
            'timeframes': ['1d', '5d', '21d'],  # Daily, weekly, monthly
            'confidence_threshold': 0.70,
            'noise_threshold': 0.15,
            'validation_window': 30  # days
        }
        
        # Scenario configuration for stocks
        self.scenario_config = {
            'base_probability': 0.60,
            'bear_probability': 0.20,
            'bull_probability': 0.20,
            'confidence_threshold': 0.65
        }
    
    def _initialize_stock_agents(self):
        """Initialize stock-focused agents with real agent instances"""
        try:
            # Initialize actual agent instances
            self.agents = {}
            
            # Initialize real quantitative agent
            try:
                from ..agents.quantitative import QuantitativeAgent
                quantitative_config = {
                    'lookback_period': 252,
                    'min_observations': 30,
                    'confidence_level': 0.95,
                    'risk_free_rate': 0.03,
                    'factor_models': ['fama_french', 'momentum']
                }
                quantitative_agent = QuantitativeAgent(quantitative_config)
                self.agents['quantitative'] = quantitative_agent
                self.logger.info("Quantitative agent initialized")
            except Exception as e:
                self.logger.warning("Failed to initialize quantitative agent", error=str(e))
                # Keep placeholder for compatibility
                self.agents['quantitative'] = {
                    'type': 'quantitative', 
                    'confidence_weight': 0.4,
                    'status': 'fallback'
                }
            
            # Initialize real technical agent
            try:
                from ..agents.technical import EnhancedTechnicalAnalysisAgent
                technical_config = {
                    'lookback_period': 100,
                    'min_observations': 20,
                    'timeframes': ['1d'],
                    'indicators': ['sma', 'rsi', 'macd']
                }
                technical_agent = EnhancedTechnicalAnalysisAgent(technical_config)
                self.agents['technical'] = technical_agent
                self.logger.info("Technical agent initialized")
            except Exception as e:
                self.logger.warning("Failed to initialize technical agent", error=str(e))
                # Keep placeholder for compatibility
                self.agents['technical'] = {
                    'type': 'technical',
                    'confidence_weight': 0.3,
                    'status': 'fallback'
                }
            
            # Market sentiment placeholder (can be implemented later)
            self.agents['sentiment'] = {
                'type': 'sentiment',
                'confidence_weight': 0.3,
                'status': 'placeholder'
            }
            
            self.logger.info("Stock-focused agents initialized", agent_count=len(self.agents))
            
        except Exception as e:
            self.logger.error("Stock agent initialization failed", error=str(e))
            # Ensure we have at least basic agents
            self.agents = {
                'technical': {'type': 'technical', 'confidence_weight': 0.5, 'status': 'fallback'},
                'quantitative': {'type': 'quantitative', 'confidence_weight': 0.5, 'status': 'fallback'}
            }
    
    async def analyze_stock_forecast(self, symbols: List[str], 
                                   include_noise_analysis: bool = True) -> Dict[str, Any]:
        """Generate comprehensive stock forecast with noise management"""
        try:
            self.logger.info("Starting stock forecast analysis", symbols=symbols)
            
            stock_forecasts = {}
            noise_analysis = {}
            
            # Process each symbol individually for better reliability
            for symbol in symbols:
                symbol_result = await self._analyze_single_stock(
                    symbol, include_noise_analysis
                )
                stock_forecasts[symbol] = symbol_result['forecast']
                if include_noise_analysis:
                    noise_analysis[symbol] = symbol_result['noise_metrics']
            
            # Generate portfolio-level insights
            portfolio_analysis = self._generate_portfolio_insights(stock_forecasts)
            
            # Risk assessment across all symbols
            portfolio_risks = self._assess_portfolio_risks(stock_forecasts)
            
            # Final integrated result
            result = {
                'timestamp': datetime.now(),
                'symbols': symbols,
                'individual_forecasts': stock_forecasts,
                'portfolio_analysis': portfolio_analysis,
                'portfolio_risks': portfolio_risks,
                'noise_analysis': noise_analysis if include_noise_analysis else {},
                'system_health': self._assess_system_health(),
                'confidence_summary': self._calculate_overall_confidence(stock_forecasts)
            }
            
            # Store for performance tracking
            self.performance_history.append(result)
            
            self.logger.info("Stock forecast analysis completed", 
                           symbols_processed=len(symbols),
                           avg_confidence=result['confidence_summary']['average_confidence'])
            
            return result
            
        except Exception as e:
            self.logger.error("Stock forecast analysis failed", error=str(e))
            raise
    
    async def _analyze_single_stock(self, symbol: str, 
                                   include_noise: bool = True) -> Dict[str, Any]:
        """Analyze a single stock with full pipeline"""
        try:
            # 1. Fetch and validate market data
            market_data = await self._fetch_market_data(symbol)
            
            # 2. Apply noise filtering if requested
            if include_noise:
                noise_result = self.noise_filter.filter_market_noise(market_data, symbol)
                clean_data = noise_result['filtered_data']
                noise_metrics = noise_result
            else:
                clean_data = market_data
                noise_metrics = {'data_quality': 0.8, 'noise_score': 0.2}
            
            # 3. Generate technical forecast
            stock_forecast = self.stock_analyzer.generate_stock_forecast(
                symbol, clean_data
            )
            
            # 4. Run agent analysis for additional insights
            agent_insights = await self._get_agent_insights(symbol, clean_data)
            
            # 5. Combine forecasts with agent insights
            integrated_forecast = self._integrate_forecasts(
                stock_forecast, agent_insights, noise_metrics
            )
            
            return {
                'forecast': integrated_forecast,
                'noise_metrics': noise_metrics,
                'data_quality': noise_metrics.get('data_quality', 0.8)
            }
            
        except Exception as e:
            self.logger.error("Single stock analysis failed", symbol=symbol, error=str(e))
            return {
                'forecast': self._create_fallback_forecast(symbol),
                'noise_metrics': {'data_quality': 0.3, 'noise_score': 0.7},
                'data_quality': 0.3
            }
    
    async def _fetch_market_data(self, symbol: str) -> pd.DataFrame:
        """Fetch market data with fallback sources"""
        try:
            # Primary: Use yfinance (free, reliable)
            import yfinance as yf
            
            ticker = yf.Ticker(symbol)
            data = ticker.history(period="6mo", interval="1d")
            
            if data.empty:
                raise ValueError(f"No data available for {symbol}")
            
            # Standardize column names
            data.columns = [col.lower() for col in data.columns]
            
            return data
            
        except Exception as e:
            self.logger.error("Market data fetch failed", symbol=symbol, error=str(e))
            raise
    
    async def _get_agent_insights(self, symbol: str, data: pd.DataFrame) -> Dict[str, Any]:
        """Get insights from initialized agents"""
        try:
            insights = {}
            
            # Get quantitative analysis insights from real agent
            if 'quantitative' in self.agents:
                quantitative_agent = self.agents['quantitative']
                
                # Check if it's a real agent instance
                if hasattr(quantitative_agent, 'analyze'):
                    try:
                        self.logger.info("Calling real quantitative agent", symbol=symbol)
                        result = await quantitative_agent.analyze([symbol], '1d')
                        
                        if result and hasattr(result, 'predictions'):
                            insights['quantitative'] = {
                                'confidence': result.confidence,
                                'predictions': result.predictions,
                                'recommendations': result.recommendations,
                                'risk_factors': result.risk_factors,
                                'agent_type': 'real'
                            }
                        else:
                            insights['quantitative'] = {'confidence': 0.3, 'agent_type': 'failed'}
                            
                    except Exception as e:
                        self.logger.warning("Real quantitative agent failed", error=str(e))
                        insights['quantitative'] = self._get_fallback_quantitative_insights(data)
                        
                else:
                    # Fallback to simple analysis
                    insights['quantitative'] = self._get_fallback_quantitative_insights(data)
            
            # Get technical analysis insights
            if 'technical' in self.agents:
                technical_agent = self.agents['technical']
                
                # Check if it's a real agent instance
                if hasattr(technical_agent, 'analyze'):
                    try:
                        self.logger.info("Calling real technical agent", symbol=symbol)
                        result = await technical_agent.analyze([symbol], '1d')
                        
                        if result and hasattr(result, 'predictions'):
                            insights['technical'] = {
                                'confidence': result.confidence,
                                'predictions': result.predictions,
                                'recommendations': result.recommendations,
                                'risk_factors': result.risk_factors,
                                'agent_type': 'real'
                            }
                        else:
                            insights['technical'] = {'confidence': 0.3, 'agent_type': 'failed'}
                            
                    except Exception as e:
                        self.logger.warning("Real technical agent failed", error=str(e))
                        insights['technical'] = self._get_fallback_technical_insights(data)
                        
                else:
                    # Fallback to simple analysis
                    insights['technical'] = self._get_fallback_technical_insights(data)
            
            return insights
            
        except Exception as e:
            self.logger.error("Agent insights failed", symbol=symbol, error=str(e))
            return {
                'technical': {'confidence': 0.3, 'agent_type': 'error'},
                'quantitative': {'confidence': 0.3, 'agent_type': 'error'}
            }
    
    def _get_fallback_quantitative_insights(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Fallback quantitative analysis when real agent fails"""
        try:
            # Simple momentum and volatility
            returns = data['close'].pct_change().dropna()
            if len(returns) >= 10:
                momentum = returns.tail(5).mean()
                volatility = returns.std()
                
                return {
                    'momentum': momentum,
                    'volatility': volatility,
                    'confidence': 0.8 if abs(momentum) > volatility else 0.5,
                    'agent_type': 'fallback'
                }
        except Exception:
            pass
        
        return {'momentum': 0, 'volatility': 0.02, 'confidence': 0.3, 'agent_type': 'fallback'}
    
    def _get_fallback_technical_insights(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Fallback technical analysis when real agent fails"""
        try:
            # Simple technical indicators
            if len(data) >= 20:
                # Moving averages
                ma_20 = data['close'].rolling(20).mean().iloc[-1]
                ma_5 = data['close'].rolling(5).mean().iloc[-1]
                current_price = data['close'].iloc[-1]
                
                # Trend signal
                trend_signal = 1 if ma_5 > ma_20 else -1 if ma_5 < ma_20 else 0
                trend_strength = abs(ma_5 - ma_20) / current_price
                
                return {
                    'trend_signal': trend_signal,
                    'trend_strength': min(trend_strength * 10, 1.0),
                    'confidence': 0.7 if trend_strength > 0.02 else 0.5,
                    'agent_type': 'fallback'
                }
        except Exception:
            pass
        
        return {'trend_signal': 0, 'trend_strength': 0, 'confidence': 0.3, 'agent_type': 'fallback'}
    
    def _integrate_forecasts(self, stock_forecast: Dict[str, Any], 
                           agent_insights: Dict[str, Any], 
                           noise_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Integrate stock forecast with agent insights"""
        try:
            # Start with the stock forecast
            integrated = stock_forecast.copy()
            
            # Adjust confidence based on agent agreement
            technical_insight = agent_insights.get('technical', {})
            quant_insight = agent_insights.get('quantitative', {})
            
            # Calculate agent agreement factor
            agent_confidence = []
            if technical_insight:
                agent_confidence.append(technical_insight.get('confidence', 0.5))
            if quant_insight:
                agent_confidence.append(quant_insight.get('confidence', 0.5))
            
            avg_agent_confidence = np.mean(agent_confidence) if agent_confidence else 0.5
            
            # Adjust forecast confidence based on agent agreement and data quality
            data_quality = noise_metrics.get('data_quality', 0.8)
            confidence_multiplier = (avg_agent_confidence * 0.3 + data_quality * 0.7)
            
            # Update all timeframe forecasts
            for timeframe in integrated.get('forecasts_by_timeframe', {}):
                forecast = integrated['forecasts_by_timeframe'][timeframe]
                original_confidence = forecast.get('confidence', 0.5)
                forecast['confidence'] = min(original_confidence * confidence_multiplier, 0.95)
            
            # Update consensus forecast
            if 'consensus_forecast' in integrated:
                consensus = integrated['consensus_forecast']
                original_confidence = consensus.get('confidence', 0.5)
                consensus['confidence'] = min(original_confidence * confidence_multiplier, 0.95)
                consensus['signal_strength'] = abs(consensus.get('expected_return', 0)) * consensus['confidence']
            
            # Add integration metadata
            integrated['integration_metadata'] = {
                'agent_insights': agent_insights,
                'data_quality': data_quality,
                'confidence_adjustment': confidence_multiplier,
                'noise_score': noise_metrics.get('noise_score', 0.2)
            }
            
            return integrated
            
        except Exception as e:
            self.logger.error("Forecast integration failed", error=str(e))
            return stock_forecast  # Return original if integration fails
    
    def _generate_portfolio_insights(self, stock_forecasts: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """Generate portfolio-level insights from individual stock forecasts"""
        try:
            if not stock_forecasts:
                return {'status': 'no_data', 'insights': []}
            
            insights = []
            total_confidence = 0
            positive_signals = 0
            negative_signals = 0
            
            for symbol, forecast_data in stock_forecasts.items():
                forecast = forecast_data.get('forecast', {})
                consensus = forecast.get('consensus_forecast', {})
                
                expected_return = consensus.get('expected_return', 0)
                confidence = consensus.get('confidence', 0.5)
                
                total_confidence += confidence
                
                if expected_return > 0.01:  # > 1% expected return
                    positive_signals += 1
                    insights.append(f"{symbol}: Bullish signal ({expected_return:.1%} expected)")
                elif expected_return < -0.01:  # < -1% expected return
                    negative_signals += 1
                    insights.append(f"{symbol}: Bearish signal ({expected_return:.1%} expected)")
                else:
                    insights.append(f"{symbol}: Neutral signal ({expected_return:.1%} expected)")
            
            avg_confidence = total_confidence / len(stock_forecasts) if stock_forecasts else 0.5
            
            # Portfolio-level insight
            if positive_signals > negative_signals:
                portfolio_sentiment = "BULLISH"
            elif negative_signals > positive_signals:
                portfolio_sentiment = "BEARISH"
            else:
                portfolio_sentiment = "NEUTRAL"
            
            return {
                'portfolio_sentiment': portfolio_sentiment,
                'average_confidence': avg_confidence,
                'positive_signals': positive_signals,
                'negative_signals': negative_signals,
                'insights': insights,
                'recommendation': f"Portfolio shows {portfolio_sentiment.lower()} sentiment with {avg_confidence:.1%} average confidence"
            }
            
        except Exception as e:
            self.logger.error("Portfolio insights generation failed", error=str(e))
            return {
                'status': 'error',
                'portfolio_sentiment': 'UNKNOWN',
                'average_confidence': 0.5,
                'insights': ['Portfolio analysis failed']
            }
    
    def _assess_portfolio_risks(self, stock_forecasts: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """Assess portfolio-level risks"""
        try:
            if not stock_forecasts:
                return {'status': 'no_data', 'risks': []}
            
            risks = []
            low_confidence_count = 0
            high_volatility_count = 0
            
            for symbol, forecast_data in stock_forecasts.items():
                forecast = forecast_data.get('forecast', {})
                consensus = forecast.get('consensus_forecast', {})
                quality = forecast.get('forecast_quality', 0.5)
                
                confidence = consensus.get('confidence', 0.5)
                
                # Check for low confidence
                if confidence < 0.6:
                    low_confidence_count += 1
                    risks.append(f"{symbol}: Low forecast confidence ({confidence:.1%})")
                
                # Check for poor forecast quality
                if quality < 0.4:
                    risks.append(f"{symbol}: Poor forecast quality ({quality:.1%})")
                
                # Check data quality if available
                data_quality = forecast_data.get('data_quality', 1.0)
                if data_quality < 0.7:
                    risks.append(f"{symbol}: Data quality concerns ({data_quality:.1%})")
            
            # Portfolio-level risk assessment
            risk_level = "LOW"
            if low_confidence_count > len(stock_forecasts) * 0.5:
                risk_level = "HIGH"
                risks.append("Portfolio: High proportion of low-confidence forecasts")
            elif low_confidence_count > len(stock_forecasts) * 0.3:
                risk_level = "MEDIUM"
                risks.append("Portfolio: Moderate forecast uncertainty")
            
            return {
                'risk_level': risk_level,
                'risk_count': len(risks),
                'risks': risks,
                'low_confidence_stocks': low_confidence_count,
                'recommendation': f"Portfolio risk level: {risk_level}"
            }
            
        except Exception as e:
            self.logger.error("Portfolio risk assessment failed", error=str(e))
            return {
                'status': 'error',
                'risk_level': 'UNKNOWN',
                'risks': ['Risk assessment failed']
            }
    
    def _assess_system_health(self) -> Dict[str, Any]:
        """Assess overall system health and performance"""
        try:
            health_metrics = {
                'status': 'operational',
                'agents_active': len(self.agents),
                'uptime': 'active',
                'memory_usage': 'normal',
                'last_forecast': datetime.now().isoformat()
            }
            
            # Check agent health
            active_agents = 0
            for agent_name, agent in self.agents.items():
                if hasattr(agent, 'analyze'):
                    # Real agent instance
                    active_agents += 1
                elif isinstance(agent, dict) and agent.get('status') == 'active':
                    # Placeholder/fallback agent
                    active_agents += 1
            
            if active_agents < len(self.agents) * 0.5:
                health_metrics['status'] = 'degraded'
                health_metrics['warning'] = 'Less than 50% of agents are active'
            
            # Check performance history
            if hasattr(self, 'performance_history') and self.performance_history:
                recent_performance = len(self.performance_history)
                health_metrics['forecasts_generated'] = recent_performance
                health_metrics['performance_trend'] = 'stable'
            else:
                health_metrics['forecasts_generated'] = 0
                health_metrics['performance_trend'] = 'unknown'
            
            return health_metrics
            
        except Exception as e:
            self.logger.error("System health assessment failed", error=str(e))
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def _calculate_overall_confidence(self, stock_forecasts: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate overall confidence metrics for the portfolio"""
        try:
            if not stock_forecasts:
                return {
                    'average_confidence': 0.5,
                    'confidence_range': (0.5, 0.5),
                    'high_confidence_count': 0,
                    'total_stocks': 0
                }
            
            confidences = []
            high_confidence_count = 0
            
            for symbol, forecast_data in stock_forecasts.items():
                forecast = forecast_data.get('forecast', {})
                consensus = forecast.get('consensus_forecast', {})
                confidence = consensus.get('confidence', 0.5)
                
                confidences.append(confidence)
                if confidence > 0.7:
                    high_confidence_count += 1
            
            avg_confidence = np.mean(confidences)
            min_confidence = min(confidences)
            max_confidence = max(confidences)
            
            return {
                'average_confidence': avg_confidence,
                'confidence_range': (min_confidence, max_confidence),
                'high_confidence_count': high_confidence_count,
                'total_stocks': len(stock_forecasts),
                'confidence_distribution': {
                    'high': sum(1 for c in confidences if c > 0.7),
                    'medium': sum(1 for c in confidences if 0.5 <= c <= 0.7),
                    'low': sum(1 for c in confidences if c < 0.5)
                }
            }
            
        except Exception as e:
            self.logger.error("Overall confidence calculation failed", error=str(e))
            return {
                'average_confidence': 0.5,
                'confidence_range': (0.5, 0.5),
                'high_confidence_count': 0,
                'total_stocks': len(stock_forecasts) if stock_forecasts else 0,
                'error': str(e)
            }
    
    def _create_fallback_forecast(self, symbol: str) -> Dict[str, Any]:
        """Create fallback forecast when analysis fails"""
        return {
            'symbol': symbol,
            'forecasts_by_timeframe': {
                '1d': {'target_price': 0, 'expected_return': 0.0, 'confidence': 0.3},
                '5d': {'target_price': 0, 'expected_return': 0.0, 'confidence': 0.3},
                '21d': {'target_price': 0, 'expected_return': 0.0, 'confidence': 0.3}
            },
            'consensus_forecast': {'expected_return': 0.0, 'confidence': 0.3, 'signal_strength': 0.0},
            'integrated_forecast': {'expected_return': 0.0, 'confidence': 0.3, 'signal_strength': 0.0},
            'confidence_intervals': {},
            'forecast_quality': 0.3,
            'data_quality_score': 0.3,
            'timestamp': datetime.now(),
            'status': 'fallback'
        }
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        try:
            if not self.performance_history:
                return {
                    'status': 'no_data',
                    'total_forecasts': 0,
                    'average_confidence': 0.5,
                    'recent_performance': 'unknown'
                }
            
            recent_forecasts = self.performance_history[-10:]  # Last 10 forecasts
            
            total_forecasts = len(self.performance_history)
            
            # Calculate average confidence from recent forecasts
            recent_confidences = []
            for forecast_result in recent_forecasts:
                confidence_summary = forecast_result.get('confidence_summary', {})
                if 'average_confidence' in confidence_summary:
                    recent_confidences.append(confidence_summary['average_confidence'])
            
            avg_recent_confidence = np.mean(recent_confidences) if recent_confidences else 0.5
            
            # Performance trend
            if len(recent_confidences) >= 2:
                recent_trend = recent_confidences[-1] - recent_confidences[0]
                trend_status = 'improving' if recent_trend > 0.05 else 'declining' if recent_trend < -0.05 else 'stable'
            else:
                trend_status = 'unknown'
            
            return {
                'status': 'active',
                'total_forecasts': total_forecasts,
                'recent_forecasts': len(recent_forecasts),
                'average_confidence': avg_recent_confidence,
                'performance_trend': trend_status,
                'last_forecast': recent_forecasts[-1]['timestamp'].isoformat() if recent_forecasts else None,
                'system_uptime': 'operational',
                'data_quality_avg': 0.8  # Placeholder
            }
            
        except Exception as e:
            self.logger.error("Performance summary failed", error=str(e))
            return {
                'status': 'error',
                'error': str(e),
                'total_forecasts': len(self.performance_history) if hasattr(self, 'performance_history') else 0
            }
