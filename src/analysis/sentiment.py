"""
Advanced Sentiment Analysis Module
Production-ready sentiment analysis using multiple data sources
Excludes Twitter/Reddit as requested, uses alternative sources
"""

import asyncio
import aiohttp
import requests
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import json
import re
from dataclasses import dataclass
import structlog
from textblob import TextBlob
import nltk
from nltk.sentiment import SentimentIntensityAnalyzer
from abc import ABC, abstractmethod

from ..core.system import MarketData, credentials, health_monitor
from ..data.sources import BaseDataSource, RateLimiter, DataSourceError

logger = structlog.get_logger()

# Download required NLTK data
try:
    nltk.download('vader_lexicon', quiet=True)
    nltk.download('punkt', quiet=True)
    nltk.download('stopwords', quiet=True)
except Exception as e:
    logger.warning("Failed to download NLTK data", error=str(e))

@dataclass
class SentimentScore:
    """Standardized sentiment score structure"""
    score: float  # -1.0 to 1.0
    confidence: float  # 0.0 to 1.0
    source: str
    timestamp: datetime
    raw_data: Dict[str, Any]
    
    def validate(self) -> bool:
        """Validate sentiment score"""
        return (
            -1.0 <= self.score <= 1.0 and
            0.0 <= self.confidence <= 1.0 and
            bool(self.source)
        )

class BaseSentimentAnalyzer(ABC):
    """Abstract base class for sentiment analyzers"""
    
    @abstractmethod
    async def analyze_text(self, text: str) -> SentimentScore:
        """Analyze sentiment of text"""
        pass
    
    @abstractmethod
    async def analyze_symbol(self, symbol: str, **kwargs) -> List[SentimentScore]:
        """Analyze sentiment for a given symbol"""
        pass

class NewsAPIAnalyzer(BaseSentimentAnalyzer):
    """News-based sentiment analysis using multiple news sources"""
    
    def __init__(self):
        self.name = "NewsAPI"
        # Using web scraping for news since we have Firecrawl API
        self.firecrawl_api_key = credentials.firecrawl_api_key
        self.sentiment_analyzer = SentimentIntensityAnalyzer()
        self.rate_limiter = RateLimiter(calls_per_minute=30, calls_per_hour=500, calls_per_day=1000)
    
    async def analyze_text(self, text: str) -> SentimentScore:
        """Analyze sentiment of individual text"""
        try:
            # Use VADER sentiment analyzer
            vader_scores = self.sentiment_analyzer.polarity_scores(text)
            
            # Use TextBlob as backup
            blob = TextBlob(text)
            textblob_polarity = blob.sentiment.polarity
            
            # Combine scores with weighting (VADER is better for financial text)
            combined_score = 0.7 * vader_scores['compound'] + 0.3 * textblob_polarity
            
            # Calculate confidence based on agreement between methods
            confidence = 1.0 - abs(vader_scores['compound'] - textblob_polarity) / 2.0
            confidence = max(0.1, min(1.0, confidence))  # Clamp between 0.1 and 1.0
            
            return SentimentScore(
                score=combined_score,
                confidence=confidence,
                source=self.name,
                timestamp=datetime.now(),
                raw_data={
                    'vader_scores': vader_scores,
                    'textblob_polarity': textblob_polarity,
                    'text_length': len(text)
                }
            )
            
        except Exception as e:
            logger.error("Text sentiment analysis failed", error=str(e))
            return SentimentScore(
                score=0.0,
                confidence=0.0,
                source=self.name,
                timestamp=datetime.now(),
                raw_data={'error': str(e)}
            )
    
    async def analyze_symbol(self, symbol: str, **kwargs) -> List[SentimentScore]:
        """Analyze sentiment for a symbol using news sources"""
        try:
            # Get recent news articles about the symbol
            news_articles = await self._fetch_news_articles(symbol, **kwargs)
            
            sentiment_scores = []
            for article in news_articles:
                if article.get('content') or article.get('title'):
                    text = f"{article.get('title', '')} {article.get('content', '')}"
                    score = await self.analyze_text(text)
                    score.raw_data.update({
                        'article_title': article.get('title'),
                        'article_url': article.get('url'),
                        'publish_date': article.get('publishedAt')
                    })
                    sentiment_scores.append(score)
            
            return sentiment_scores
            
        except Exception as e:
            logger.error("Symbol sentiment analysis failed", symbol=symbol, error=str(e))
            return []
    
    async def _fetch_news_articles(self, symbol: str, days_back: int = 7) -> List[Dict[str, Any]]:
        """Fetch news articles for a symbol"""
        articles = []
        
        try:
            # Use multiple news sources
            news_sources = [
                f"https://finance.yahoo.com/quote/{symbol}/news",
                f"https://www.marketwatch.com/investing/stock/{symbol}",
                f"https://seekingalpha.com/symbol/{symbol}/news",
                f"https://www.bloomberg.com/quote/{symbol}:US"
            ]
            
            for source_url in news_sources:
                try:
                    scraped_articles = await self._scrape_news_from_url(source_url, symbol)
                    articles.extend(scraped_articles)
                except Exception as e:
                    logger.warning("Failed to scrape news source", url=source_url, error=str(e))
            
            # Also use Tavily API for additional news search
            if credentials.tavily_api_key:
                tavily_articles = await self._search_tavily_news(symbol, days_back)
                articles.extend(tavily_articles)
            
            return articles[:50]  # Limit to 50 most relevant articles
            
        except Exception as e:
            logger.error("News fetching failed", symbol=symbol, error=str(e))
            return []
    
    async def _scrape_news_from_url(self, url: str, symbol: str) -> List[Dict[str, Any]]:
        """Scrape news articles from a URL using Firecrawl API"""
        if not self.firecrawl_api_key:
            return []
        
        try:
            # Wait for rate limit
            while not self.rate_limiter.can_make_call():
                await asyncio.sleep(1)
            
            self.rate_limiter.record_call()
            
            # Use Firecrawl API to scrape the page
            async with aiohttp.ClientSession() as session:
                headers = {
                    'Authorization': f'Bearer {self.firecrawl_api_key}',
                    'Content-Type': 'application/json'
                }
                
                payload = {
                    'url': url,
                    'formats': ['markdown'],
                    'timeout': 30000
                }
                
                async with session.post(
                    'https://api.firecrawl.dev/v1/scrape',
                    headers=headers,
                    json=payload,
                    timeout=60
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        content = result.get('data', {}).get('markdown', '')
                        
                        # Extract article-like content mentioning the symbol
                        articles = self._extract_articles_from_content(content, symbol)
                        return articles
                    else:
                        logger.warning("Firecrawl API error", status=response.status, url=url)
                        return []
                        
        except Exception as e:
            logger.error("Web scraping failed", url=url, error=str(e))
            return []
    
    def _extract_articles_from_content(self, content: str, symbol: str) -> List[Dict[str, Any]]:
        """Extract article-like content from scraped web page"""
        articles = []
        
        try:
            # Split content into paragraphs
            paragraphs = content.split('\n\n')
            
            # Find paragraphs that mention the symbol
            relevant_paragraphs = []
            for para in paragraphs:
                if symbol.upper() in para.upper() and len(para) > 50:
                    relevant_paragraphs.append(para.strip())
            
            # Group related paragraphs into articles
            current_article = ""
            for para in relevant_paragraphs:
                if len(current_article) > 1000:  # Start new article
                    if current_article:
                        articles.append({
                            'title': current_article[:100] + "...",
                            'content': current_article,
                            'url': 'scraped_content',
                            'publishedAt': datetime.now().isoformat()
                        })
                    current_article = para
                else:
                    current_article += "\n\n" + para
            
            # Add the last article
            if current_article:
                articles.append({
                    'title': current_article[:100] + "...",
                    'content': current_article,
                    'url': 'scraped_content',
                    'publishedAt': datetime.now().isoformat()
                })
            
            return articles[:10]  # Limit to 10 articles per source
            
        except Exception as e:
            logger.error("Content extraction failed", error=str(e))
            return []
    
    async def _search_tavily_news(self, symbol: str, days_back: int = 7) -> List[Dict[str, Any]]:
        """Search for news using Tavily API"""
        if not credentials.tavily_api_key:
            return []
        
        try:
            # Wait for rate limit
            while not self.rate_limiter.can_make_call():
                await asyncio.sleep(1)
            
            self.rate_limiter.record_call()
            
            query = f"{symbol} stock earnings financial news"
            
            async with aiohttp.ClientSession() as session:
                headers = {
                    'Content-Type': 'application/json'
                }
                
                payload = {
                    'api_key': credentials.tavily_api_key,
                    'query': query,
                    'search_depth': 'advanced',
                    'max_results': 20,
                    'include_domains': [
                        'finance.yahoo.com',
                        'marketwatch.com',
                        'bloomberg.com',
                        'reuters.com',
                        'cnbc.com',
                        'seekingalpha.com'
                    ]
                }
                
                async with session.post(
                    'https://api.tavily.com/search',
                    headers=headers,
                    json=payload,
                    timeout=30
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        
                        articles = []
                        for item in result.get('results', []):
                            articles.append({
                                'title': item.get('title', ''),
                                'content': item.get('content', ''),
                                'url': item.get('url', ''),
                                'publishedAt': datetime.now().isoformat()  # Tavily doesn't provide dates
                            })
                        
                        return articles
                    else:
                        logger.warning("Tavily API error", status=response.status)
                        return []
                        
        except Exception as e:
            logger.error("Tavily search failed", symbol=symbol, error=str(e))
            return []

class SECFilingAnalyzer(BaseSentimentAnalyzer):
    """Sentiment analysis of SEC filings"""
    
    def __init__(self):
        self.name = "SEC_Filings"
        self.api_key = credentials.sec_api_key
        self.sentiment_analyzer = SentimentIntensityAnalyzer()
        self.rate_limiter = RateLimiter(calls_per_minute=10, calls_per_hour=100, calls_per_day=1000)
    
    async def analyze_text(self, text: str) -> SentimentScore:
        """Analyze sentiment of SEC filing text"""
        try:
            # Clean and preprocess SEC text
            cleaned_text = self._clean_sec_text(text)
            
            # Use VADER for financial sentiment
            scores = self.sentiment_analyzer.polarity_scores(cleaned_text)
            
            # SEC filings have specific language patterns
            sentiment_score = self._adjust_for_sec_language(scores, cleaned_text)
            
            return SentimentScore(
                score=sentiment_score,
                confidence=scores['compound'] if abs(scores['compound']) > 0.1 else 0.5,
                source=self.name,
                timestamp=datetime.now(),
                raw_data={
                    'vader_scores': scores,
                    'text_length': len(cleaned_text),
                    'original_length': len(text)
                }
            )
            
        except Exception as e:
            logger.error("SEC text analysis failed", error=str(e))
            return SentimentScore(
                score=0.0,
                confidence=0.0,
                source=self.name,
                timestamp=datetime.now(),
                raw_data={'error': str(e)}
            )
    
    async def analyze_symbol(self, symbol: str, **kwargs) -> List[SentimentScore]:
        """Analyze sentiment from recent SEC filings"""
        try:
            filings = await self._fetch_recent_filings(symbol, **kwargs)
            
            sentiment_scores = []
            for filing in filings:
                if filing.get('content'):
                    score = await self.analyze_text(filing['content'])
                    score.raw_data.update({
                        'filing_type': filing.get('form'),
                        'filing_date': filing.get('filedAt'),
                        'company_name': filing.get('companyName')
                    })
                    sentiment_scores.append(score)
            
            return sentiment_scores
            
        except Exception as e:
            logger.error("SEC filing analysis failed", symbol=symbol, error=str(e))
            return []
    
    def _clean_sec_text(self, text: str) -> str:
        """Clean SEC filing text for sentiment analysis"""
        # Remove common SEC boilerplate
        text = re.sub(r'UNITED STATES.*?COMMISSION', '', text, flags=re.DOTALL)
        text = re.sub(r'SECURITIES AND EXCHANGE COMMISSION', '', text)
        text = re.sub(r'FORM \d+-[A-Z]', '', text)
        
        # Remove excessive whitespace and formatting
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'[^\w\s.,!?;:\'"()-]', '', text)
        
        # Focus on management discussion and analysis sections
        mda_patterns = [
            r'(?i)management.{0,50}discussion.{0,50}analysis.*?(?=item\s+\d+|$)',
            r'(?i)business\s+overview.*?(?=item\s+\d+|$)',
            r'(?i)risk\s+factors.*?(?=item\s+\d+|$)'
        ]
        
        relevant_sections = []
        for pattern in mda_patterns:
            matches = re.findall(pattern, text, re.DOTALL)
            relevant_sections.extend(matches)
        
        if relevant_sections:
            return ' '.join(relevant_sections)[:5000]  # Limit length
        else:
            return text[:5000]  # Use first 5000 chars if no specific sections found
    
    def _adjust_for_sec_language(self, vader_scores: Dict[str, float], text: str) -> float:
        """Adjust sentiment for SEC-specific language patterns"""
        base_score = vader_scores['compound']
        
        # SEC filings often use cautious language that shouldn't be interpreted as negative
        cautious_phrases = [
            'may', 'might', 'could', 'potential', 'uncertain', 'risk', 'forward-looking'
        ]
        
        text_lower = text.lower()
        cautious_count = sum(text_lower.count(phrase) for phrase in cautious_phrases)
        cautious_ratio = cautious_count / len(text.split()) if text.split() else 0
        
        # Reduce negative bias from cautious language
        if base_score < 0 and cautious_ratio > 0.02:  # More than 2% cautious words
            adjustment = min(0.3, cautious_ratio * 10)  # Scale adjustment
            base_score = base_score * (1 - adjustment)
        
        # Look for specific positive indicators in financial context
        positive_financial_terms = [
            'revenue growth', 'profit increase', 'strong performance', 'exceeded expectations',
            'positive outlook', 'expansion', 'innovation', 'competitive advantage'
        ]
        
        positive_count = sum(text_lower.count(term) for term in positive_financial_terms)
        if positive_count > 0:
            base_score += min(0.2, positive_count * 0.05)
        
        return max(-1.0, min(1.0, base_score))
    
    async def _fetch_recent_filings(self, symbol: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Fetch recent SEC filings for a symbol"""
        if not self.api_key:
            return []
        
        try:
            # Wait for rate limit
            while not self.rate_limiter.can_make_call():
                await asyncio.sleep(1)
            
            self.rate_limiter.record_call()
            
            # Get company CIK first
            cik = await self._get_company_cik(symbol)
            if not cik:
                return []
            
            # Search for recent filings
            url = "https://api.sec-api.io"
            headers = {"Authorization": self.api_key}
            
            # Get recent 10-K and 10-Q filings
            query = {
                "query": {
                    "query_string": {
                        "query": f"cik:{cik} AND (formType:\"10-K\" OR formType:\"10-Q\")"
                    }
                },
                "from": "0",
                "size": str(limit),
                "sort": [{"filedAt": {"order": "desc"}}]
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{url}/filings",
                    headers=headers,
                    json=query,
                    timeout=30
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        
                        filings = []
                        for filing in result.get('filings', []):
                            # Get filing content
                            content = await self._get_filing_content(filing.get('linkToTxt', ''))
                            filing['content'] = content
                            filings.append(filing)
                        
                        return filings
                    else:
                        logger.warning("SEC API error", status=response.status)
                        return []
                        
        except Exception as e:
            logger.error("SEC filing fetch failed", symbol=symbol, error=str(e))
            return []
    
    async def _get_company_cik(self, symbol: str) -> Optional[str]:
        """Get company CIK number from symbol"""
        try:
            # This is a simplified lookup - in production, use proper CIK mapping
            url = f"https://www.sec.gov/files/company_tickers.json"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        for company in data.values():
                            if company.get('ticker', '').upper() == symbol.upper():
                                return str(company.get('cik_str', '')).zfill(10)
                        
                        return None
                    else:
                        return None
                        
        except Exception as e:
            logger.error("CIK lookup failed", symbol=symbol, error=str(e))
            return None
    
    async def _get_filing_content(self, filing_url: str) -> str:
        """Get content from SEC filing URL"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(filing_url, timeout=30) as response:
                    if response.status == 200:
                        content = await response.text()
                        return content[:10000]  # Limit to first 10k chars
                    else:
                        return ""
                        
        except Exception as e:
            logger.error("Filing content fetch failed", url=filing_url, error=str(e))
            return ""

class EarningsCallAnalyzer(BaseSentimentAnalyzer):
    """Sentiment analysis of earnings call transcripts"""
    
    def __init__(self):
        self.name = "EarningsCall"
        self.sentiment_analyzer = SentimentIntensityAnalyzer()
        self.rate_limiter = RateLimiter(calls_per_minute=20, calls_per_hour=200, calls_per_day=1000)
    
    async def analyze_text(self, text: str) -> SentimentScore:
        """Analyze sentiment of earnings call transcript"""
        try:
            # Clean earnings call text
            cleaned_text = self._clean_earnings_text(text)
            
            # Use VADER with earnings-specific adjustments
            scores = self.sentiment_analyzer.polarity_scores(cleaned_text)
            
            # Adjust for earnings call specific language
            sentiment_score = self._adjust_for_earnings_language(scores, cleaned_text)
            
            return SentimentScore(
                score=sentiment_score,
                confidence=min(1.0, abs(scores['compound']) + 0.2),  # Higher confidence for earnings calls
                source=self.name,
                timestamp=datetime.now(),
                raw_data={
                    'vader_scores': scores,
                    'text_length': len(cleaned_text),
                    'management_tone': self._analyze_management_tone(cleaned_text)
                }
            )
            
        except Exception as e:
            logger.error("Earnings call analysis failed", error=str(e))
            return SentimentScore(
                score=0.0,
                confidence=0.0,
                source=self.name,
                timestamp=datetime.now(),
                raw_data={'error': str(e)}
            )
    
    async def analyze_symbol(self, symbol: str, **kwargs) -> List[SentimentScore]:
        """Analyze sentiment from recent earnings calls"""
        try:
            # For now, use web scraping to get earnings call information
            # In production, integrate with earnings call transcript APIs
            transcripts = await self._fetch_earnings_transcripts(symbol, **kwargs)
            
            sentiment_scores = []
            for transcript in transcripts:
                if transcript.get('content'):
                    score = await self.analyze_text(transcript['content'])
                    score.raw_data.update({
                        'call_date': transcript.get('date'),
                        'quarter': transcript.get('quarter'),
                        'fiscal_year': transcript.get('fiscal_year')
                    })
                    sentiment_scores.append(score)
            
            return sentiment_scores
            
        except Exception as e:
            logger.error("Earnings call sentiment analysis failed", symbol=symbol, error=str(e))
            return []
    
    def _clean_earnings_text(self, text: str) -> str:
        """Clean earnings call transcript for sentiment analysis"""
        # Remove speaker labels and formatting
        text = re.sub(r'^\w+\s+\w+:', '', text, flags=re.MULTILINE)
        text = re.sub(r'\[.*?\]', '', text)  # Remove stage directions
        text = re.sub(r'\(.*?\)', '', text)  # Remove parenthetical comments
        
        # Focus on management commentary (not Q&A)
        management_section = re.search(
            r'(?i)prepared\s+remarks.*?(?=question|q&a|$)', 
            text, 
            re.DOTALL
        )
        
        if management_section:
            return management_section.group(0)[:5000]
        else:
            return text[:5000]
    
    def _adjust_for_earnings_language(self, vader_scores: Dict[str, float], text: str) -> float:
        """Adjust sentiment for earnings call specific language"""
        base_score = vader_scores['compound']
        
        # Identify key financial performance indicators
        text_lower = text.lower()
        
        positive_indicators = [
            'beat expectations', 'exceeded guidance', 'strong growth', 'record revenue',
            'margin expansion', 'ahead of schedule', 'outperformed', 'accelerating growth'
        ]
        
        negative_indicators = [
            'below expectations', 'missed guidance', 'headwinds', 'challenging environment',
            'margin pressure', 'delayed', 'underperformed', 'slowing growth'
        ]
        
        positive_count = sum(text_lower.count(indicator) for indicator in positive_indicators)
        negative_count = sum(text_lower.count(indicator) for indicator in negative_indicators)
        
        # Adjust score based on financial indicators
        if positive_count > negative_count:
            base_score += min(0.3, (positive_count - negative_count) * 0.1)
        elif negative_count > positive_count:
            base_score -= min(0.3, (negative_count - positive_count) * 0.1)
        
        return max(-1.0, min(1.0, base_score))
    
    def _analyze_management_tone(self, text: str) -> str:
        """Analyze the overall tone of management commentary"""
        text_lower = text.lower()
        
        confident_phrases = ['confident', 'optimistic', 'excited', 'pleased', 'strong position']
        cautious_phrases = ['cautious', 'uncertain', 'challenging', 'difficult', 'headwinds']
        
        confident_count = sum(text_lower.count(phrase) for phrase in confident_phrases)
        cautious_count = sum(text_lower.count(phrase) for phrase in cautious_phrases)
        
        if confident_count > cautious_count:
            return "confident"
        elif cautious_count > confident_count:
            return "cautious"
        else:
            return "neutral"
    
    async def _fetch_earnings_transcripts(self, symbol: str, quarters_back: int = 2) -> List[Dict[str, Any]]:
        """Fetch earnings call transcripts (placeholder implementation)"""
        # This is a placeholder - in production, integrate with:
        # - Seeking Alpha API
        # - FactSet API
        # - Bloomberg API
        # - AlphaSense API
        
        transcripts = []
        
        try:
            # Use web scraping to get basic earnings information
            if credentials.firecrawl_api_key:
                earnings_urls = [
                    f"https://seekingalpha.com/symbol/{symbol}/earnings/transcripts",
                    f"https://finance.yahoo.com/quote/{symbol}/analysis"
                ]
                
                for url in earnings_urls:
                    try:
                        content = await self._scrape_earnings_content(url, symbol)
                        if content:
                            transcripts.append({
                                'content': content,
                                'date': datetime.now().strftime('%Y-%m-%d'),
                                'quarter': 'Q4',  # Placeholder
                                'fiscal_year': '2023'  # Placeholder
                            })
                    except Exception as e:
                        logger.warning("Failed to scrape earnings content", url=url, error=str(e))
            
            return transcripts[:quarters_back]
            
        except Exception as e:
            logger.error("Earnings transcript fetch failed", symbol=symbol, error=str(e))
            return []
    
    async def _scrape_earnings_content(self, url: str, symbol: str) -> str:
        """Scrape earnings-related content from URL"""
        if not credentials.firecrawl_api_key:
            return ""
        
        try:
            # Wait for rate limit
            while not self.rate_limiter.can_make_call():
                await asyncio.sleep(1)
            
            self.rate_limiter.record_call()
            
            # Use Firecrawl API
            async with aiohttp.ClientSession() as session:
                headers = {
                    'Authorization': f'Bearer {credentials.firecrawl_api_key}',
                    'Content-Type': 'application/json'
                }
                
                payload = {
                    'url': url,
                    'formats': ['markdown'],
                    'timeout': 30000
                }
                
                async with session.post(
                    'https://api.firecrawl.dev/v1/scrape',
                    headers=headers,
                    json=payload,
                    timeout=60
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        content = result.get('data', {}).get('markdown', '')
                        
                        # Extract earnings-related content
                        earnings_content = self._extract_earnings_content(content, symbol)
                        return earnings_content
                    else:
                        return ""
                        
        except Exception as e:
            logger.error("Earnings scraping failed", url=url, error=str(e))
            return ""
    
    def _extract_earnings_content(self, content: str, symbol: str) -> str:
        """Extract earnings-specific content from scraped page"""
        # Look for earnings-related keywords and extract surrounding context
        earnings_keywords = [
            'earnings', 'revenue', 'guidance', 'outlook', 'quarterly results',
            'financial results', 'performance', 'conference call'
        ]
        
        relevant_content = []
        paragraphs = content.split('\n\n')
        
        for para in paragraphs:
            para_lower = para.lower()
            if (symbol.lower() in para_lower and 
                any(keyword in para_lower for keyword in earnings_keywords) and
                len(para) > 100):
                relevant_content.append(para)
        
        return '\n\n'.join(relevant_content[:5])  # Limit to 5 most relevant paragraphs

class CompositeSentimentAnalyzer:
    """Composite sentiment analyzer that combines multiple sources"""
    
    def __init__(self):
        self.analyzers = {
            'news': NewsAPIAnalyzer(),
            'sec_filings': SECFilingAnalyzer(),
            'earnings_calls': EarningsCallAnalyzer()
        }
        
    async def analyze_symbol_comprehensive(
        self, 
        symbol: str, 
        sources: List[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Comprehensive sentiment analysis using multiple sources"""
        
        if sources is None:
            sources = list(self.analyzers.keys())
        
        results = {}
        all_scores = []
        
        # Run analysis from each source
        for source_name in sources:
            if source_name in self.analyzers:
                try:
                    analyzer = self.analyzers[source_name]
                    scores = await analyzer.analyze_symbol(symbol, **kwargs)
                    results[source_name] = scores
                    all_scores.extend(scores)
                    
                    logger.info(
                        "Sentiment analysis completed",
                        source=source_name,
                        symbol=symbol,
                        score_count=len(scores)
                    )
                    
                except Exception as e:
                    logger.error(
                        "Sentiment analysis failed",
                        source=source_name,
                        symbol=symbol,
                        error=str(e)
                    )
                    results[source_name] = []
        
        # Calculate composite sentiment
        composite_sentiment = self._calculate_composite_sentiment(all_scores)
        
        return {
            'symbol': symbol,
            'timestamp': datetime.now(),
            'individual_sources': results,
            'composite_sentiment': composite_sentiment,
            'total_data_points': len(all_scores),
            'source_coverage': list(results.keys())
        }
    
    def _calculate_composite_sentiment(self, scores: List[SentimentScore]) -> Dict[str, float]:
        """Calculate weighted composite sentiment from multiple sources"""
        if not scores:
            return {
                'score': 0.0,
                'confidence': 0.0,
                'source_weights': {}
            }
        
        # Define source weights (higher weight = more reliable)
        source_weights = {
            'SEC_Filings': 0.4,  # Most reliable, official company communication
            'EarningsCall': 0.35,  # High reliability, direct management communication
            'NewsAPI': 0.25  # Good for market sentiment, but can be noisy
        }
        
        # Calculate weighted average
        weighted_scores = []
        total_weight = 0
        confidence_sum = 0
        
        for score in scores:
            weight = source_weights.get(score.source, 0.2)  # Default weight
            if score.validate():
                weighted_scores.append(score.score * weight * score.confidence)
                total_weight += weight * score.confidence
                confidence_sum += score.confidence * weight
        
        if total_weight > 0:
            composite_score = sum(weighted_scores) / total_weight
            composite_confidence = confidence_sum / sum(source_weights.get(s.source, 0.2) for s in scores)
        else:
            composite_score = 0.0
            composite_confidence = 0.0
        
        return {
            'score': composite_score,
            'confidence': min(1.0, composite_confidence),
            'source_weights': {s.source: source_weights.get(s.source, 0.2) for s in scores}
        }

# Initialize global sentiment analyzer
sentiment_analyzer = CompositeSentimentAnalyzer()

logger.info("Advanced sentiment analysis module initialized",
           available_analyzers=list(sentiment_analyzer.analyzers.keys()))
