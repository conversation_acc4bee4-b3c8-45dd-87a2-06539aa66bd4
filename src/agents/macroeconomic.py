"""
Macroeconomic Analysis Agent
Analyzes economic indicators, policy impacts, and macro trends
Uses FRED, Bureau of Economic Analysis, and other economic data sources
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import structlog
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA

from .base import BaseAgent, AgentResult, AgentError, MultiSourceAgent
from ..core.system import credentials
from ..data.sources import FREDSource, DataSourceRegistry

logger = structlog.get_logger()

class MacroeconomicAgent(MultiSourceAgent):
    """Agent specializing in macroeconomic analysis"""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__("macroeconomic", config)
        
        # Key economic indicators to track
        self.key_indicators = {
            'gdp': 'GDP',
            'inflation': 'CPIAUCSL',  # Consumer Price Index
            'unemployment': 'UNRATE',
            'interest_rates': 'FEDFUNDS',  # Federal Funds Rate
            'consumer_confidence': 'UMCSENT',  # University of Michigan Consumer Sentiment
            'retail_sales': 'RSAFS',  # Retail Sales
            'industrial_production': 'INDPRO',
            'housing_starts': 'HOUST',
            'ppi': 'PPIACO',  # Producer Price Index
            'money_supply': 'M2SL'  # M2 Money Supply
        }
        
        # Economic sectors and their sensitivity to macro factors
        self.sector_sensitivity = {
            'financial': {'interest_rates': 0.8, 'inflation': 0.6, 'gdp': 0.7},
            'consumer_discretionary': {'unemployment': -0.7, 'consumer_confidence': 0.8, 'inflation': -0.5},
            'utilities': {'interest_rates': -0.6, 'inflation': -0.4},
            'real_estate': {'interest_rates': -0.9, 'housing_starts': 0.8},
            'technology': {'gdp': 0.6, 'consumer_confidence': 0.5},
            'healthcare': {'gdp': 0.4, 'inflation': -0.3},
            'energy': {'inflation': 0.7, 'industrial_production': 0.6}
        }
        
        self.scaler = StandardScaler()
        self.pca = PCA(n_components=5)  # Reduce dimensionality of economic indicators
    
    def _validate_config(self) -> bool:
        """Validate macroeconomic agent configuration"""
        required_keys = ['lookback_period', 'forecast_horizon']
        
        if not all(key in self.config for key in required_keys):
            return False
        
        lookback = self.config.get('lookback_period', 252)  # Default 1 year
        forecast = self.config.get('forecast_horizon', 30)   # Default 30 days
        
        return lookback > 0 and forecast > 0
    
    async def _initialize_resources(self):
        """Initialize economic data sources"""
        try:
            # Initialize FRED data source
            fred_source = FREDSource()
            self.add_data_source('fred', fred_source)
            
            # Add other economic data sources if available
            registry = DataSourceRegistry()
            
            # Initialize with sample data for development
            self._load_sample_economic_data()
            
            self.logger.info("Macroeconomic agent resources initialized")
            
        except Exception as e:
            self.logger.error("Failed to initialize macroeconomic resources", error=str(e))
            raise
    
    async def _perform_analysis(self, symbols: List[str], timeframe: str, **kwargs) -> AgentResult:
        """Perform comprehensive macroeconomic analysis"""
        try:
            # Collect economic data
            economic_data = await self._collect_economic_indicators()
            
            # Analyze current economic regime
            regime_analysis = self._analyze_economic_regime(economic_data)
            
            # Assess policy impacts
            policy_impact = self._assess_policy_impacts(economic_data)
            
            # Generate sector rotation signals
            sector_signals = self._generate_sector_rotation_signals(economic_data, symbols)
            
            # Calculate macro risk factors
            risk_factors = self._calculate_macro_risk_factors(economic_data)
            
            # Generate forecasts
            forecasts = self._generate_macro_forecasts(economic_data)
            
            # Assess symbol-specific impacts
            symbol_impacts = await self._assess_symbol_impacts(symbols, economic_data, sector_signals)
            
            # Calculate overall confidence based on data quality and model performance
            confidence = self._calculate_confidence(economic_data, regime_analysis)
            
            # Generate recommendations
            recommendations = self._generate_recommendations(
                regime_analysis, policy_impact, sector_signals, symbol_impacts
            )
            
            # Compile predictions
            predictions = {
                'economic_regime': regime_analysis,
                'policy_impact': policy_impact,
                'sector_rotation': sector_signals,
                'macro_forecasts': forecasts,
                'symbol_impacts': symbol_impacts
            }
            
            return AgentResult(
                agent_name=self.agent_name,
                analysis_type="macroeconomic",
                timestamp=datetime.now(),
                symbols=symbols,
                confidence=confidence,
                predictions=predictions,
                recommendations=recommendations,
                risk_factors=risk_factors,
                data_quality=self._assess_data_quality(economic_data),
                metadata={
                    'indicators_analyzed': len(economic_data),
                    'lookback_period': self.config.get('lookback_period', 252),
                    'forecast_horizon': self.config.get('forecast_horizon', 30)
                }
            )
            
        except Exception as e:
            self.logger.error("Macroeconomic analysis failed", error=str(e))
            raise AgentError(f"Analysis failed: {str(e)}", self.agent_name, "ANALYSIS_ERROR")
    
    async def _fetch_from_source(self, source: Any, symbols: List[str], **kwargs) -> Any:
        """Fetch data from economic data source"""
        if hasattr(source, 'get_economic_indicators'):
            return await source.get_economic_indicators(list(self.key_indicators.values()))
        else:
            # Fallback to sample data
            return self._generate_sample_data()
    
    async def _collect_economic_indicators(self) -> Dict[str, pd.DataFrame]:
        """Collect key economic indicators"""
        lookback_days = self.config.get('lookback_period', 252)
        end_date = datetime.now()
        start_date = end_date - timedelta(days=lookback_days)
        
        indicators_data = {}
        
        try:
            # Try to get real data from sources
            source_data = await self._collect_data_from_sources([])
            
            for indicator_name, fred_code in self.key_indicators.items():
                # Use real data if available, otherwise use sample data
                if 'fred' in source_data and source_data['fred']:
                    indicators_data[indicator_name] = source_data['fred'].get(fred_code, self._generate_indicator_sample(indicator_name))
                else:
                    indicators_data[indicator_name] = self._generate_indicator_sample(indicator_name)
        
        except Exception as e:
            self.logger.warning("Using sample economic data", error=str(e))
            # Use sample data as fallback
            for indicator_name in self.key_indicators.keys():
                indicators_data[indicator_name] = self._generate_indicator_sample(indicator_name)
        
        return indicators_data
    
    def _analyze_economic_regime(self, economic_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """Analyze current economic regime (expansion, contraction, stagflation, etc.)"""
        try:
            # Calculate key metrics
            gdp_growth = self._calculate_growth_rate(economic_data.get('gdp'))
            inflation_rate = self._calculate_inflation_rate(economic_data.get('inflation'))
            unemployment_trend = self._calculate_trend(economic_data.get('unemployment'))
            interest_rate_level = self._get_current_level(economic_data.get('interest_rates'))
            
            # Determine regime
            if gdp_growth > 0.02 and inflation_rate < 0.04:  # Healthy growth
                regime = "expansion"
                strength = 0.8
            elif gdp_growth < 0 and unemployment_trend > 0:  # Recession
                regime = "contraction"
                strength = 0.7
            elif inflation_rate > 0.05 and gdp_growth < 0.01:  # Stagflation
                regime = "stagflation"
                strength = 0.6
            elif interest_rate_level < 0.02:  # Low rate environment
                regime = "low_rate_expansion"
                strength = 0.7
            else:
                regime = "transition"
                strength = 0.5
            
            return {
                'regime': regime,
                'strength': strength,
                'gdp_growth': gdp_growth,
                'inflation_rate': inflation_rate,
                'unemployment_trend': unemployment_trend,
                'interest_rate_level': interest_rate_level,
                'confidence': min(0.9, strength + 0.1)
            }
            
        except Exception as e:
            self.logger.error("Economic regime analysis failed", error=str(e))
            return {
                'regime': 'unknown',
                'strength': 0.3,
                'confidence': 0.3,
                'error': str(e)
            }
    
    def _assess_policy_impacts(self, economic_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """Assess potential policy impacts on markets"""
        try:
            fed_funds_rate = self._get_current_level(economic_data.get('interest_rates'))
            inflation_rate = self._calculate_inflation_rate(economic_data.get('inflation'))
            unemployment_rate = self._get_current_level(economic_data.get('unemployment'))
            
            # Assess monetary policy stance
            if fed_funds_rate < inflation_rate:  # Accommodative
                monetary_stance = "accommodative"
                equity_impact = 0.6  # Positive for equities
            elif fed_funds_rate > inflation_rate + 0.02:  # Restrictive
                monetary_stance = "restrictive"
                equity_impact = -0.4  # Negative for equities
            else:
                monetary_stance = "neutral"
                equity_impact = 0.1
            
            # Assess fiscal policy needs
            if unemployment_rate > 0.06:  # High unemployment
                fiscal_stimulus_likelihood = 0.7
            elif inflation_rate > 0.04:  # High inflation
                fiscal_stimulus_likelihood = 0.2
            else:
                fiscal_stimulus_likelihood = 0.4
            
            return {
                'monetary_stance': monetary_stance,
                'equity_impact': equity_impact,
                'fiscal_stimulus_likelihood': fiscal_stimulus_likelihood,
                'fed_funds_rate': fed_funds_rate,
                'policy_confidence': 0.75
            }
            
        except Exception as e:
            self.logger.error("Policy impact assessment failed", error=str(e))
            return {
                'monetary_stance': 'unknown',
                'equity_impact': 0.0,
                'fiscal_stimulus_likelihood': 0.5,
                'policy_confidence': 0.3
            }
    
    def _generate_sector_rotation_signals(self, economic_data: Dict[str, pd.DataFrame], symbols: List[str]) -> Dict[str, Any]:
        """Generate sector rotation signals based on economic conditions"""
        try:
            signals = {}
            
            # Calculate current economic conditions
            conditions = {}
            for indicator, data in economic_data.items():
                if data is not None and not data.empty:
                    conditions[indicator] = self._normalize_indicator(data)
            
            # Generate signals for each sector
            for sector, sensitivities in self.sector_sensitivity.items():
                sector_score = 0.0
                weight_sum = 0.0
                
                for indicator, sensitivity in sensitivities.items():
                    if indicator in conditions:
                        sector_score += sensitivity * conditions[indicator]
                        weight_sum += abs(sensitivity)
                
                if weight_sum > 0:
                    normalized_score = sector_score / weight_sum
                    signals[sector] = {
                        'score': normalized_score,
                        'signal': 'buy' if normalized_score > 0.3 else 'sell' if normalized_score < -0.3 else 'hold',
                        'confidence': min(0.9, abs(normalized_score) + 0.1)
                    }
            
            return {
                'sector_signals': signals,
                'rotation_strength': max(abs(s['score']) for s in signals.values()) if signals else 0.0,
                'signal_confidence': 0.7
            }
            
        except Exception as e:
            self.logger.error("Sector rotation analysis failed", error=str(e))
            return {
                'sector_signals': {},
                'rotation_strength': 0.0,
                'signal_confidence': 0.3
            }
    
    def _calculate_macro_risk_factors(self, economic_data: Dict[str, pd.DataFrame]) -> List[str]:
        """Calculate macroeconomic risk factors"""
        risk_factors = []
        
        try:
            # Check inflation risk
            inflation_rate = self._calculate_inflation_rate(economic_data.get('inflation'))
            if inflation_rate > 0.05:
                risk_factors.append(f"High inflation risk: {inflation_rate:.2%}")
            
            # Check recession risk
            gdp_growth = self._calculate_growth_rate(economic_data.get('gdp'))
            if gdp_growth < -0.01:
                risk_factors.append(f"Recession risk: GDP growth {gdp_growth:.2%}")
            
            # Check interest rate risk
            interest_rate = self._get_current_level(economic_data.get('interest_rates'))
            if interest_rate > 0.06:
                risk_factors.append(f"High interest rate environment: {interest_rate:.2%}")
            
            # Check unemployment risk
            unemployment = self._get_current_level(economic_data.get('unemployment'))
            if unemployment > 0.07:
                risk_factors.append(f"High unemployment: {unemployment:.2%}")
            
            # Check consumer confidence
            consumer_conf = self._get_current_level(economic_data.get('consumer_confidence'))
            if consumer_conf and consumer_conf < 80:  # Assuming index scale
                risk_factors.append(f"Low consumer confidence: {consumer_conf:.1f}")
            
        except Exception as e:
            self.logger.error("Risk factor calculation failed", error=str(e))
            risk_factors.append("Data quality issues in macro analysis")
        
        return risk_factors if risk_factors else ["No significant macro risks identified"]
    
    def _generate_macro_forecasts(self, economic_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """Generate macroeconomic forecasts"""
        forecasts = {}
        forecast_horizon = self.config.get('forecast_horizon', 30)
        
        try:
            for indicator, data in economic_data.items():
                if data is not None and not data.empty and len(data) > 10:
                    forecast = self._simple_forecast(data, forecast_horizon)
                    forecasts[indicator] = forecast
            
            return {
                'individual_forecasts': forecasts,
                'forecast_horizon_days': forecast_horizon,
                'forecast_confidence': 0.6  # Conservative confidence for macro forecasts
            }
            
        except Exception as e:
            self.logger.error("Macro forecasting failed", error=str(e))
            return {
                'individual_forecasts': {},
                'forecast_horizon_days': forecast_horizon,
                'forecast_confidence': 0.3
            }
    
    async def _assess_symbol_impacts(self, symbols: List[str], economic_data: Dict[str, pd.DataFrame], 
                                   sector_signals: Dict[str, Any]) -> Dict[str, Any]:
        """Assess macroeconomic impacts on specific symbols"""
        symbol_impacts = {}
        
        try:
            for symbol in symbols:
                # Determine symbol's sector (simplified mapping)
                sector = self._map_symbol_to_sector(symbol)
                
                # Get sector signal
                sector_signal = sector_signals.get('sector_signals', {}).get(sector, {})
                
                # Calculate macro sensitivity
                macro_sensitivity = self._calculate_symbol_macro_sensitivity(symbol, economic_data)
                
                # Generate impact assessment
                impact_score = sector_signal.get('score', 0.0) * macro_sensitivity
                
                symbol_impacts[symbol] = {
                    'sector': sector,
                    'macro_sensitivity': macro_sensitivity,
                    'impact_score': impact_score,
                    'recommendation': sector_signal.get('signal', 'hold'),
                    'confidence': sector_signal.get('confidence', 0.5) * 0.8  # Reduce confidence for individual symbols
                }
            
            return symbol_impacts
            
        except Exception as e:
            self.logger.error("Symbol impact assessment failed", error=str(e))
            return {symbol: {'impact_score': 0.0, 'confidence': 0.3} for symbol in symbols}
    
    # Helper methods
    def _calculate_growth_rate(self, data: pd.DataFrame) -> float:
        """Calculate growth rate from time series data"""
        if data is None or data.empty or len(data) < 2:
            return 0.0
        
        try:
            values = data.iloc[:, 0] if len(data.columns) > 0 else data
            if len(values) < 2:
                return 0.0
            
            return (values.iloc[-1] - values.iloc[-2]) / values.iloc[-2] if values.iloc[-2] != 0 else 0.0
        except:
            return 0.0
    
    def _calculate_inflation_rate(self, data: pd.DataFrame) -> float:
        """Calculate inflation rate from CPI data"""
        if data is None or data.empty or len(data) < 12:
            return 0.03  # Default assumption
        
        try:
            values = data.iloc[:, 0] if len(data.columns) > 0 else data
            if len(values) < 12:
                return 0.03
            
            return (values.iloc[-1] - values.iloc[-13]) / values.iloc[-13] if values.iloc[-13] != 0 else 0.03
        except:
            return 0.03
    
    def _calculate_trend(self, data: pd.DataFrame) -> float:
        """Calculate trend (slope) of time series"""
        if data is None or data.empty or len(data) < 3:
            return 0.0
        
        try:
            values = data.iloc[:, 0] if len(data.columns) > 0 else data
            if len(values) < 3:
                return 0.0
            
            x = np.arange(len(values))
            slope, _, _, _, _ = stats.linregress(x, values)
            return slope
        except:
            return 0.0
    
    def _get_current_level(self, data: pd.DataFrame) -> float:
        """Get current level of indicator"""
        if data is None or data.empty:
            return 0.0
        
        try:
            values = data.iloc[:, 0] if len(data.columns) > 0 else data
            return float(values.iloc[-1]) if len(values) > 0 else 0.0
        except:
            return 0.0
    
    def _normalize_indicator(self, data: pd.DataFrame) -> float:
        """Normalize indicator to [-1, 1] range based on recent history"""
        if data is None or data.empty:
            return 0.0
        
        try:
            values = data.iloc[:, 0] if len(data.columns) > 0 else data
            if len(values) < 2:
                return 0.0
            
            current = values.iloc[-1]
            mean_val = values.mean()
            std_val = values.std()
            
            if std_val == 0:
                return 0.0
            
            z_score = (current - mean_val) / std_val
            return np.tanh(z_score)  # Bounded to [-1, 1]
        except:
            return 0.0
    
    def _simple_forecast(self, data: pd.DataFrame, horizon: int) -> Dict[str, float]:
        """Simple trend-based forecast"""
        try:
            values = data.iloc[:, 0] if len(data.columns) > 0 else data
            if len(values) < 3:
                return {'forecast': float(values.iloc[-1]) if len(values) > 0 else 0.0, 'confidence': 0.3}
            
            # Simple linear extrapolation
            x = np.arange(len(values))
            slope, intercept, r_value, _, _ = stats.linregress(x, values)
            
            forecast_x = len(values) + horizon - 1
            forecast_value = slope * forecast_x + intercept
            
            return {
                'forecast': forecast_value,
                'confidence': min(0.8, abs(r_value))  # Use correlation as confidence proxy
            }
        except:
            return {'forecast': 0.0, 'confidence': 0.3}
    
    def _map_symbol_to_sector(self, symbol: str) -> str:
        """Map symbol to sector (simplified)"""
        # Simplified sector mapping - in production, use real sector data
        tech_symbols = ['AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA', 'META', 'NVDA']
        financial_symbols = ['JPM', 'BAC', 'WFC', 'GS', 'MS']
        healthcare_symbols = ['JNJ', 'PFE', 'UNH', 'ABBV', 'MRK']
        
        if symbol in tech_symbols:
            return 'technology'
        elif symbol in financial_symbols:
            return 'financial'
        elif symbol in healthcare_symbols:
            return 'healthcare'
        else:
            return 'consumer_discretionary'  # Default
    
    def _calculate_symbol_macro_sensitivity(self, symbol: str, economic_data: Dict[str, pd.DataFrame]) -> float:
        """Calculate symbol's sensitivity to macro factors"""
        # Simplified calculation - in production, use beta analysis with macro factors
        sector = self._map_symbol_to_sector(symbol)
        
        if sector in self.sector_sensitivity:
            # Average absolute sensitivity across all macro factors for the sector
            sensitivities = list(self.sector_sensitivity[sector].values())
            return np.mean([abs(s) for s in sensitivities])
        
        return 0.5  # Default moderate sensitivity
    
    def _calculate_confidence(self, economic_data: Dict[str, pd.DataFrame], regime_analysis: Dict[str, Any]) -> float:
        """Calculate overall analysis confidence"""
        try:
            # Base confidence on data availability and quality
            data_coverage = sum(1 for data in economic_data.values() if data is not None and not data.empty)
            max_coverage = len(self.key_indicators)
            
            coverage_ratio = data_coverage / max_coverage
            regime_confidence = regime_analysis.get('confidence', 0.5)
            
            # Combine factors
            overall_confidence = (coverage_ratio * 0.4 + regime_confidence * 0.6)
            
            return min(0.95, max(0.3, overall_confidence))  # Bound between 30% and 95%
            
        except:
            return 0.5
    
    def _assess_data_quality(self, economic_data: Dict[str, pd.DataFrame]) -> float:
        """Assess quality of economic data"""
        if not economic_data:
            return 0.0
        
        try:
            quality_scores = []
            
            for indicator, data in economic_data.items():
                if data is None or data.empty:
                    quality_scores.append(0.0)
                    continue
                
                # Check data completeness and recency
                completeness = 1.0 - (data.isnull().sum().sum() / (len(data) * len(data.columns)))
                
                # Assume recent data is higher quality
                recency_score = 1.0  # Simplified - in production, check actual timestamps
                
                quality_scores.append((completeness + recency_score) / 2)
            
            return np.mean(quality_scores) if quality_scores else 0.0
            
        except:
            return 0.5
    
    def _generate_recommendations(self, regime_analysis: Dict[str, Any], policy_impact: Dict[str, Any],
                                sector_signals: Dict[str, Any], symbol_impacts: Dict[str, Any]) -> List[str]:
        """Generate actionable recommendations"""
        recommendations = []
        
        try:
            regime = regime_analysis.get('regime', 'unknown')
            
            # Regime-based recommendations
            if regime == 'expansion':
                recommendations.append("Favor growth and cyclical sectors in expansionary environment")
            elif regime == 'contraction':
                recommendations.append("Shift to defensive sectors and quality dividend stocks")
            elif regime == 'stagflation':
                recommendations.append("Consider real assets and inflation-protected securities")
            elif regime == 'low_rate_expansion':
                recommendations.append("Leverage benefits of low interest rates for growth investments")
            
            # Policy-based recommendations
            monetary_stance = policy_impact.get('monetary_stance', 'unknown')
            if monetary_stance == 'accommodative':
                recommendations.append("Accommodative monetary policy supports risk assets")
            elif monetary_stance == 'restrictive':
                recommendations.append("Restrictive policy may pressure valuations - be selective")
            
            # Sector rotation recommendations
            sector_signals_data = sector_signals.get('sector_signals', {})
            strong_sectors = [sector for sector, data in sector_signals_data.items() 
                            if data.get('signal') == 'buy' and data.get('confidence', 0) > 0.6]
            
            if strong_sectors:
                recommendations.append(f"Consider overweighting: {', '.join(strong_sectors)}")
            
            # Risk management
            if not recommendations:
                recommendations.append("Maintain balanced allocation given mixed macro signals")
            
            recommendations.append("Monitor economic data releases for regime changes")
            
        except Exception as e:
            self.logger.error("Recommendation generation failed", error=str(e))
            recommendations.append("Maintain conservative positioning due to analysis limitations")
        
        return recommendations
    
    # Sample data generation methods for development/testing
    def _load_sample_economic_data(self):
        """Load sample economic data for development"""
        self.sample_data = {}
        for indicator in self.key_indicators.keys():
            self.sample_data[indicator] = self._generate_indicator_sample(indicator)
    
    def _generate_indicator_sample(self, indicator: str) -> pd.DataFrame:
        """Generate sample data for an economic indicator"""
        days = self.config.get('lookback_period', 252)
        dates = pd.date_range(end=datetime.now(), periods=days, freq='D')
        
        # Different patterns for different indicators
        if indicator == 'gdp':
            # Quarterly GDP growth, interpolated daily
            base_value = 20000  # Billion dollars
            growth_rate = 0.02 / 365  # 2% annual growth
            noise = np.random.normal(0, 0.001, days)
            values = base_value * (1 + growth_rate) ** np.arange(days) + noise
            
        elif indicator == 'inflation':
            # CPI index with some volatility
            base_value = 280  # CPI index
            trend = 0.03 / 365  # 3% annual inflation
            seasonal = 0.01 * np.sin(2 * np.pi * np.arange(days) / 365)
            noise = np.random.normal(0, 0.002, days)
            values = base_value * (1 + trend) ** np.arange(days) * (1 + seasonal + noise)
            
        elif indicator == 'unemployment':
            # Unemployment rate with cyclical patterns
            base_rate = 0.05  # 5% base rate
            cycle = 0.02 * np.sin(2 * np.pi * np.arange(days) / (365 * 4))  # 4-year cycle
            noise = np.random.normal(0, 0.001, days)
            values = base_rate + cycle + noise
            
        elif indicator == 'interest_rates':
            # Federal funds rate
            base_rate = 0.025  # 2.5% base rate
            policy_changes = np.random.choice([-0.0025, 0, 0.0025], days, p=[0.1, 0.8, 0.1])
            values = np.maximum(0, np.cumsum(policy_changes) + base_rate)
            
        else:
            # Generic indicator with trend and noise
            base_value = 100
            trend = np.random.normal(0.0001, 0.0005, days)
            noise = np.random.normal(0, 0.01, days)
            values = base_value + np.cumsum(trend + noise)
        
        return pd.DataFrame({
            'date': dates,
            'value': values
        }).set_index('date')
    
    def _generate_sample_data(self) -> Dict[str, Any]:
        """Generate sample data for fallback"""
        return {indicator: self._generate_indicator_sample(indicator) 
                for indicator in self.key_indicators.keys()}
