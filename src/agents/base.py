"""
Base Agent Classes and Interfaces
Production-grade agent architecture with validation and error handling
"""

import asyncio
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
import structlog
import traceback

from ..core.system import MarketData, health_monitor
from ..data.sources import DataSourceError

logger = structlog.get_logger()

@dataclass
class AgentResult:
    """Standardized agent result structure"""
    agent_name: str
    analysis_type: str
    timestamp: datetime
    symbols: List[str]
    confidence: float  # 0.0 to 1.0
    predictions: Dict[str, Any]
    recommendations: List[str]
    risk_factors: List[str]
    data_quality: float  # 0.0 to 1.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def validate(self) -> bool:
        """Validate agent result"""
        try:
            return (
                bool(self.agent_name) and
                bool(self.analysis_type) and
                isinstance(self.timestamp, datetime) and
                isinstance(self.symbols, list) and
                0.0 <= self.confidence <= 1.0 and
                0.0 <= self.data_quality <= 1.0 and
                isinstance(self.predictions, dict) and
                isinstance(self.recommendations, list) and
                isinstance(self.risk_factors, list)
            )
        except Exception as e:
            logger.error("Agent result validation failed", error=str(e))
            return False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'agent_name': self.agent_name,
            'analysis_type': self.analysis_type,
            'timestamp': self.timestamp.isoformat(),
            'symbols': self.symbols,
            'confidence': self.confidence,
            'predictions': self.predictions,
            'recommendations': self.recommendations,
            'risk_factors': self.risk_factors,
            'data_quality': self.data_quality,
            'metadata': self.metadata
        }

class AgentError(Exception):
    """Custom exception for agent errors"""
    def __init__(self, message: str, agent_name: str = None, error_code: str = None):
        super().__init__(message)
        self.agent_name = agent_name
        self.error_code = error_code
        self.timestamp = datetime.now()

class BaseAgent(ABC):
    """Base class for all specialized agents"""
    
    def __init__(self, agent_name: str, config: Dict[str, Any] = None):
        self.agent_name = agent_name
        self.config = config or {}
        self.logger = structlog.get_logger().bind(agent=agent_name)
        self.is_initialized = False
        self._performance_metrics = {
            'total_analyses': 0,
            'successful_analyses': 0,
            'average_confidence': 0.0,
            'average_data_quality': 0.0,
            'last_analysis': None
        }
    
    async def initialize(self) -> bool:
        """Initialize agent resources and validate configuration"""
        try:
            self.logger.info("Initializing agent", config=self.config)
            
            # Validate configuration
            if not self._validate_config():
                raise AgentError("Invalid configuration", self.agent_name, "CONFIG_ERROR")
            
            # Initialize resources
            await self._initialize_resources()
            
            self.is_initialized = True
            health_monitor.update_component_health(f"agent_{self.agent_name}", True)
            
            self.logger.info("Agent initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error("Agent initialization failed", error=str(e), traceback=traceback.format_exc())
            health_monitor.update_component_health(f"agent_{self.agent_name}", False)
            raise AgentError(f"Initialization failed: {str(e)}", self.agent_name, "INIT_ERROR")
    
    async def analyze(self, symbols: List[str], timeframe: str = "1d", **kwargs) -> AgentResult:
        """Perform analysis on given symbols"""
        if not self.is_initialized:
            await self.initialize()
        
        start_time = datetime.now()
        
        try:
            self.logger.info("Starting analysis", symbols=symbols, timeframe=timeframe)
            
            # Validate inputs
            if not symbols:
                raise AgentError("No symbols provided", self.agent_name, "INPUT_ERROR")
            
            # Perform analysis
            result = await self._perform_analysis(symbols, timeframe, **kwargs)
            
            # Validate result
            if not result.validate():
                raise AgentError("Invalid analysis result", self.agent_name, "RESULT_ERROR")
            
            # Update metrics
            self._update_performance_metrics(result, success=True)
            
            self.logger.info("Analysis completed successfully", 
                           duration=(datetime.now() - start_time).total_seconds(),
                           confidence=result.confidence,
                           data_quality=result.data_quality)
            
            return result
            
        except Exception as e:
            self._update_performance_metrics(None, success=False)
            self.logger.error("Analysis failed", 
                            symbols=symbols,
                            error=str(e), 
                            traceback=traceback.format_exc(),
                            duration=(datetime.now() - start_time).total_seconds())
            
            if isinstance(e, AgentError):
                raise
            else:
                raise AgentError(f"Analysis failed: {str(e)}", self.agent_name, "ANALYSIS_ERROR")
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get agent performance metrics"""
        return self._performance_metrics.copy()
    
    def reset_metrics(self):
        """Reset performance metrics"""
        self._performance_metrics = {
            'total_analyses': 0,
            'successful_analyses': 0,
            'average_confidence': 0.0,
            'average_data_quality': 0.0,
            'last_analysis': None
        }
    
    @abstractmethod
    def _validate_config(self) -> bool:
        """Validate agent configuration"""
        pass
    
    @abstractmethod
    async def _initialize_resources(self):
        """Initialize agent-specific resources"""
        pass
    
    @abstractmethod
    async def _perform_analysis(self, symbols: List[str], timeframe: str, **kwargs) -> AgentResult:
        """Perform agent-specific analysis"""
        pass
    
    def _update_performance_metrics(self, result: Optional[AgentResult], success: bool):
        """Update performance metrics"""
        self._performance_metrics['total_analyses'] += 1
        
        if success and result:
            self._performance_metrics['successful_analyses'] += 1
            
            # Update running averages
            total_successful = self._performance_metrics['successful_analyses']
            
            current_avg_conf = self._performance_metrics['average_confidence']
            new_conf = result.confidence
            self._performance_metrics['average_confidence'] = (
                (current_avg_conf * (total_successful - 1) + new_conf) / total_successful
            )
            
            current_avg_quality = self._performance_metrics['average_data_quality']
            new_quality = result.data_quality
            self._performance_metrics['average_data_quality'] = (
                (current_avg_quality * (total_successful - 1) + new_quality) / total_successful
            )
            
            self._performance_metrics['last_analysis'] = datetime.now().isoformat()

class MultiSourceAgent(BaseAgent):
    """Base class for agents that use multiple data sources"""
    
    def __init__(self, agent_name: str, config: Dict[str, Any] = None):
        super().__init__(agent_name, config)
        self.data_sources = {}
        config = config or {}
        self.min_sources_required = config.get('min_sources_required', 1)
    
    def add_data_source(self, name: str, source: Any):
        """Add a data source to the agent"""
        self.data_sources[name] = source
        self.logger.info("Data source added", source_name=name, total_sources=len(self.data_sources))
    
    async def _collect_data_from_sources(self, symbols: List[str], **kwargs) -> Dict[str, Any]:
        """Collect data from all configured sources"""
        if len(self.data_sources) < self.min_sources_required:
            raise AgentError(
                f"Insufficient data sources: {len(self.data_sources)} < {self.min_sources_required}",
                self.agent_name,
                "INSUFFICIENT_SOURCES"
            )
        
        results = {}
        successful_sources = 0
        
        for source_name, source in self.data_sources.items():
            try:
                self.logger.debug("Fetching data from source", source_name=source_name)
                data = await self._fetch_from_source(source, symbols, **kwargs)
                results[source_name] = data
                successful_sources += 1
                
            except Exception as e:
                self.logger.warning("Data source failed", 
                                  source_name=source_name, 
                                  error=str(e))
                results[source_name] = None
        
        if successful_sources < self.min_sources_required:
            raise AgentError(
                f"Too few successful sources: {successful_sources} < {self.min_sources_required}",
                self.agent_name,
                "SOURCE_FAILURES"
            )
        
        return results
    
    @abstractmethod
    async def _fetch_from_source(self, source: Any, symbols: List[str], **kwargs) -> Any:
        """Fetch data from a specific source"""
        pass
