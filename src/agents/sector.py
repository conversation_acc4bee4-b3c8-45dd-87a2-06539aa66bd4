"""
Sector Analysis Agent
Analyzes sector-specific trends, rotation patterns, and relative performance
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import structlog
from scipy import stats
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler

from .base import BaseAgent, AgentResult, AgentError, MultiSourceAgent
from ..core.system import credentials
from ..data.sources import DataSourceRegistry

logger = structlog.get_logger()

class SectorAnalysisAgent(MultiSourceAgent):
    """Agent specializing in sector analysis and rotation patterns"""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__("sector_analysis", config)
        
        # Define major sectors and their characteristics
        self.sectors = {
            'technology': {
                'symbols': ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META', 'NVDA', 'TSLA'],
                'characteristics': {
                    'growth_oriented': True,
                    'interest_rate_sensitive': True,
                    'cyclical': True,
                    'innovation_driven': True
                }
            },
            'healthcare': {
                'symbols': ['JNJ', 'UNH', 'PFE', 'ABBV', 'MRK', 'TMO', 'DHR'],
                'characteristics': {
                    'growth_oriented': True,
                    'interest_rate_sensitive': False,
                    'cyclical': False,
                    'defensive': True
                }
            },
            'financial': {
                'symbols': ['JPM', 'BAC', 'WFC', 'GS', 'MS', 'C', 'BRK.B'],
                'characteristics': {
                    'growth_oriented': False,
                    'interest_rate_sensitive': True,
                    'cyclical': True,
                    'value_oriented': True
                }
            },
            'consumer_discretionary': {
                'symbols': ['AMZN', 'TSLA', 'HD', 'MCD', 'NKE', 'SBUX', 'DIS'],
                'characteristics': {
                    'growth_oriented': True,
                    'interest_rate_sensitive': True,
                    'cyclical': True,
                    'consumer_dependent': True
                }
            },
            'consumer_staples': {
                'symbols': ['PG', 'KO', 'PEP', 'WMT', 'COST', 'CL', 'KMB'],
                'characteristics': {
                    'growth_oriented': False,
                    'interest_rate_sensitive': False,
                    'cyclical': False,
                    'defensive': True
                }
            },
            'energy': {
                'symbols': ['XOM', 'CVX', 'COP', 'EOG', 'SLB', 'MPC', 'VLO'],
                'characteristics': {
                    'growth_oriented': False,
                    'interest_rate_sensitive': False,
                    'cyclical': True,
                    'commodity_driven': True
                }
            },
            'utilities': {
                'symbols': ['NEE', 'DUK', 'SO', 'D', 'EXC', 'SRE', 'AEP'],
                'characteristics': {
                    'growth_oriented': False,
                    'interest_rate_sensitive': True,
                    'cyclical': False,
                    'defensive': True
                }
            },
            'industrials': {
                'symbols': ['BA', 'CAT', 'GE', 'MMM', 'HON', 'UPS', 'RTX'],
                'characteristics': {
                    'growth_oriented': True,
                    'interest_rate_sensitive': True,
                    'cyclical': True,
                    'global_exposure': True
                }
            },
            'materials': {
                'symbols': ['LIN', 'APD', 'ECL', 'DD', 'DOW', 'NEM', 'FCX'],
                'characteristics': {
                    'growth_oriented': False,
                    'interest_rate_sensitive': True,
                    'cyclical': True,
                    'commodity_driven': True
                }
            },
            'real_estate': {
                'symbols': ['AMT', 'PLD', 'CCI', 'EQIX', 'PSA', 'SPG', 'O'],
                'characteristics': {
                    'growth_oriented': False,
                    'interest_rate_sensitive': True,
                    'cyclical': True,
                    'income_oriented': True
                }
            }
        }
        
        # Sector rotation patterns based on economic cycles
        self.rotation_patterns = {
            'early_cycle': ['technology', 'consumer_discretionary', 'industrials'],
            'mid_cycle': ['technology', 'healthcare', 'industrials'],
            'late_cycle': ['energy', 'materials', 'financial'],
            'recession': ['consumer_staples', 'utilities', 'healthcare']
        }
        
        self.scaler = StandardScaler()
        self.kmeans = KMeans(n_clusters=3, random_state=42)  # For performance clustering
    
    def _validate_config(self) -> bool:
        """Validate sector analysis configuration"""
        required_keys = ['lookback_period', 'min_sector_correlation']
        
        if not all(key in self.config for key in required_keys):
            return False
        
        lookback = self.config.get('lookback_period', 60)
        correlation = self.config.get('min_sector_correlation', 0.5)
        
        return lookback > 0 and 0 <= correlation <= 1
    
    async def _initialize_resources(self):
        """Initialize sector analysis resources"""
        try:
            # Initialize data sources
            registry = DataSourceRegistry()
            
            # Get available data sources
            available_sources = ['alpha_vantage', 'finnhub']  # Based on our existing sources
            
            for source_name in available_sources:
                try:
                    source = registry.get_source(source_name)
                    if source:
                        self.add_data_source(source_name, source)
                except Exception as e:
                    self.logger.warning(f"Could not initialize {source_name}", error=str(e))
            
            # Generate sample data for development
            self._load_sample_sector_data()
            
            self.logger.info("Sector analysis agent resources initialized")
            
        except Exception as e:
            self.logger.error("Failed to initialize sector analysis resources", error=str(e))
            raise
    
    async def _perform_analysis(self, symbols: List[str], timeframe: str, **kwargs) -> AgentResult:
        """Perform comprehensive sector analysis"""
        try:
            # Collect sector performance data
            sector_data = await self._collect_sector_data(symbols, timeframe)
            
            # Analyze sector performance
            performance_analysis = self._analyze_sector_performance(sector_data)
            
            # Detect rotation patterns
            rotation_analysis = self._detect_rotation_patterns(sector_data)
            
            # Analyze sector correlations
            correlation_analysis = self._analyze_sector_correlations(sector_data)
            
            # Generate relative strength analysis
            relative_strength = self._calculate_relative_strength(sector_data)
            
            # Identify sector leaders and laggards
            leaders_laggards = self._identify_leaders_laggards(sector_data, performance_analysis)
            
            # Generate sector allocation recommendations
            allocation_recs = self._generate_allocation_recommendations(
                performance_analysis, rotation_analysis, relative_strength
            )
            
            # Calculate risk factors
            risk_factors = self._calculate_sector_risk_factors(correlation_analysis, sector_data)
            
            # Calculate confidence
            confidence = self._calculate_confidence(sector_data, performance_analysis)
            
            # Generate recommendations
            recommendations = self._generate_recommendations(
                performance_analysis, rotation_analysis, allocation_recs, leaders_laggards
            )
            
            # Compile predictions
            predictions = {
                'sector_performance': performance_analysis,
                'rotation_patterns': rotation_analysis,
                'sector_correlations': correlation_analysis,
                'relative_strength': relative_strength,
                'leaders_laggards': leaders_laggards,
                'allocation_recommendations': allocation_recs
            }
            
            return AgentResult(
                agent_name=self.agent_name,
                analysis_type="sector_analysis",
                timestamp=datetime.now(),
                symbols=symbols,
                confidence=confidence,
                predictions=predictions,
                recommendations=recommendations,
                risk_factors=risk_factors,
                data_quality=self._assess_data_quality(sector_data),
                metadata={
                    'sectors_analyzed': len(sector_data),
                    'lookback_period': self.config.get('lookback_period', 60),
                    'timeframe': timeframe
                }
            )
            
        except Exception as e:
            self.logger.error("Sector analysis failed", error=str(e))
            raise AgentError(f"Analysis failed: {str(e)}", self.agent_name, "ANALYSIS_ERROR")
    
    async def _fetch_from_source(self, source: Any, symbols: List[str], **kwargs) -> Any:
        """Fetch data from sector data source"""
        timeframe = kwargs.get('timeframe', '1d')
        lookback = self.config.get('lookback_period', 60)
        
        try:
            # Get all sector symbols
            all_symbols = []
            for sector_info in self.sectors.values():
                all_symbols.extend(sector_info['symbols'])
            
            # Remove duplicates and filter to requested symbols if specified
            all_symbols = list(set(all_symbols))
            if symbols:
                all_symbols = [s for s in all_symbols if s in symbols]
            
            # Fetch data
            if hasattr(source, 'get_historical_data'):
                return await source.get_historical_data(all_symbols, timeframe, lookback)
            else:
                return self._generate_sample_sector_data(all_symbols, lookback)
                
        except Exception as e:
            self.logger.warning("Data fetch failed, using sample data", error=str(e))
            return self._generate_sample_sector_data(symbols or [], lookback)
    
    async def _collect_sector_data(self, symbols: List[str], timeframe: str) -> Dict[str, pd.DataFrame]:
        """Collect price data for all sectors"""
        try:
            # Collect data from sources
            source_data = await self._collect_data_from_sources(symbols, timeframe=timeframe)
            
            # Organize data by sector
            sector_data = {}
            
            for sector_name, sector_info in self.sectors.items():
                sector_symbols = sector_info['symbols']
                
                # Filter symbols if specific ones were requested
                if symbols:
                    sector_symbols = [s for s in sector_symbols if s in symbols]
                
                if not sector_symbols:
                    continue
                
                # Aggregate sector data from available sources
                sector_prices = {}
                
                for source_name, data in source_data.items():
                    if data:
                        for symbol in sector_symbols:
                            if symbol in data:
                                sector_prices[symbol] = data[symbol]
                
                if sector_prices:
                    # Calculate sector index (equal-weighted average)
                    sector_df = pd.DataFrame(sector_prices)
                    sector_data[sector_name] = sector_df.mean(axis=1).to_frame('price')
                else:
                    # Use sample data
                    sector_data[sector_name] = self._generate_sample_sector_price(sector_name)
            
            return sector_data
            
        except Exception as e:
            self.logger.error("Sector data collection failed", error=str(e))
            # Return sample data as fallback
            return {sector: self._generate_sample_sector_price(sector) for sector in self.sectors.keys()}
    
    def _analyze_sector_performance(self, sector_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """Analyze sector performance metrics"""
        try:
            performance = {}
            
            for sector, data in sector_data.items():
                if data.empty:
                    continue
                
                prices = data['price']
                
                # Calculate returns
                returns = prices.pct_change().dropna()
                
                # Performance metrics
                total_return = (prices.iloc[-1] / prices.iloc[0] - 1) if len(prices) > 1 else 0
                volatility = returns.std() * np.sqrt(252)  # Annualized
                sharpe_ratio = (returns.mean() * 252) / volatility if volatility > 0 else 0
                max_drawdown = self._calculate_max_drawdown(prices)
                
                # Recent performance (last 5, 10, 20 days)
                recent_performance = {}
                for days in [5, 10, 20]:
                    if len(prices) >= days:
                        recent_return = (prices.iloc[-1] / prices.iloc[-days] - 1)
                        recent_performance[f'{days}d'] = recent_return
                
                performance[sector] = {
                    'total_return': total_return,
                    'volatility': volatility,
                    'sharpe_ratio': sharpe_ratio,
                    'max_drawdown': max_drawdown,
                    'recent_performance': recent_performance,
                    'current_price': prices.iloc[-1] if len(prices) > 0 else 0
                }
            
            # Rank sectors by performance
            if performance:
                sorted_sectors = sorted(performance.items(), 
                                      key=lambda x: x[1]['total_return'], 
                                      reverse=True)
                
                for i, (sector, data) in enumerate(sorted_sectors):
                    performance[sector]['rank'] = i + 1
                    performance[sector]['percentile'] = (len(sorted_sectors) - i) / len(sorted_sectors)
            
            return {
                'sector_metrics': performance,
                'analysis_period': self.config.get('lookback_period', 60),
                'best_performer': sorted_sectors[0][0] if performance else None,
                'worst_performer': sorted_sectors[-1][0] if performance else None
            }
            
        except Exception as e:
            self.logger.error("Sector performance analysis failed", error=str(e))
            return {'sector_metrics': {}, 'analysis_period': 0}
    
    def _detect_rotation_patterns(self, sector_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """Detect sector rotation patterns"""
        try:
            if len(sector_data) < 2:
                return {'rotation_detected': False, 'pattern': None}
            
            # Calculate relative performance matrix
            relative_performance = {}
            for sector, data in sector_data.items():
                if not data.empty:
                    returns = data['price'].pct_change().dropna()
                    if len(returns) > 0:
                        relative_performance[sector] = returns.rolling(window=10).mean().iloc[-1]
            
            if len(relative_performance) < 2:
                return {'rotation_detected': False, 'pattern': None}
            
            # Identify current cycle based on sector performance
            current_leaders = sorted(relative_performance.items(), 
                                   key=lambda x: x[1], reverse=True)[:3]
            leading_sectors = [sector for sector, _ in current_leaders]
            
            # Match to known rotation patterns
            best_match = None
            best_score = 0
            
            for pattern_name, pattern_sectors in self.rotation_patterns.items():
                # Calculate overlap score
                overlap = len(set(leading_sectors) & set(pattern_sectors))
                score = overlap / len(pattern_sectors)
                
                if score > best_score:
                    best_score = score
                    best_match = pattern_name
            
            rotation_strength = self._calculate_rotation_strength(relative_performance)
            
            return {
                'rotation_detected': best_score > 0.5,
                'pattern': best_match,
                'pattern_confidence': best_score,
                'leading_sectors': leading_sectors,
                'rotation_strength': rotation_strength,
                'cycle_stage': best_match if best_score > 0.5 else 'unclear'
            }
            
        except Exception as e:
            self.logger.error("Rotation pattern detection failed", error=str(e))
            return {'rotation_detected': False, 'pattern': None}
    
    def _analyze_sector_correlations(self, sector_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """Analyze correlations between sectors"""
        try:
            if len(sector_data) < 2:
                return {'correlation_matrix': {}, 'diversification_score': 0}
            
            # Prepare returns data
            returns_data = {}
            for sector, data in sector_data.items():
                if not data.empty:
                    returns = data['price'].pct_change().dropna()
                    if len(returns) > 10:  # Minimum data points
                        returns_data[sector] = returns
            
            if len(returns_data) < 2:
                return {'correlation_matrix': {}, 'diversification_score': 0}
            
            # Align data
            returns_df = pd.DataFrame(returns_data).dropna()
            
            if returns_df.empty:
                return {'correlation_matrix': {}, 'diversification_score': 0}
            
            # Calculate correlation matrix
            correlation_matrix = returns_df.corr()
            
            # Calculate diversification score (lower correlation = better diversification)
            avg_correlation = correlation_matrix.values[np.triu_indices_from(correlation_matrix.values, k=1)].mean()
            diversification_score = max(0, 1 - avg_correlation)
            
            # Identify highly correlated sector pairs
            high_corr_pairs = []
            threshold = self.config.get('min_sector_correlation', 0.7)
            
            for i in range(len(correlation_matrix)):
                for j in range(i+1, len(correlation_matrix)):
                    corr = correlation_matrix.iloc[i, j]
                    if abs(corr) > threshold:
                        high_corr_pairs.append({
                            'sector1': correlation_matrix.index[i],
                            'sector2': correlation_matrix.index[j],
                            'correlation': corr
                        })
            
            return {
                'correlation_matrix': correlation_matrix.to_dict(),
                'average_correlation': avg_correlation,
                'diversification_score': diversification_score,
                'high_correlation_pairs': high_corr_pairs,
                'analysis_period': len(returns_df)
            }
            
        except Exception as e:
            self.logger.error("Correlation analysis failed", error=str(e))
            return {'correlation_matrix': {}, 'diversification_score': 0}
    
    def _calculate_relative_strength(self, sector_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """Calculate relative strength of sectors vs market"""
        try:
            if not sector_data:
                return {'relative_strength': {}}
            
            # Create market proxy (equal-weighted average of all sectors)
            all_prices = []
            for data in sector_data.values():
                if not data.empty:
                    all_prices.append(data['price'])
            
            if not all_prices:
                return {'relative_strength': {}}
            
            market_proxy = pd.concat(all_prices, axis=1).mean(axis=1)
            market_returns = market_proxy.pct_change().dropna()
            
            relative_strength = {}
            
            for sector, data in sector_data.items():
                if data.empty:
                    continue
                
                sector_returns = data['price'].pct_change().dropna()
                
                # Calculate relative strength (sector vs market)
                # Align data
                aligned_data = pd.concat([sector_returns, market_returns], axis=1).dropna()
                if len(aligned_data) < 10:
                    continue
                
                sector_aligned = aligned_data.iloc[:, 0]
                market_aligned = aligned_data.iloc[:, 1]
                
                # Calculate beta and alpha
                if market_aligned.std() > 0:
                    beta = np.cov(sector_aligned, market_aligned)[0, 1] / np.var(market_aligned)
                    alpha = sector_aligned.mean() - beta * market_aligned.mean()
                else:
                    beta = 1.0
                    alpha = 0.0
                
                # Calculate relative strength score
                recent_rel_perf = (sector_aligned.iloc[-20:].mean() - market_aligned.iloc[-20:].mean()) if len(sector_aligned) >= 20 else 0
                
                relative_strength[sector] = {
                    'beta': beta,
                    'alpha': alpha,
                    'recent_relative_performance': recent_rel_perf,
                    'relative_strength_score': alpha + recent_rel_perf
                }
            
            # Rank by relative strength
            if relative_strength:
                sorted_rs = sorted(relative_strength.items(), 
                                 key=lambda x: x[1]['relative_strength_score'], 
                                 reverse=True)
                
                for i, (sector, data) in enumerate(sorted_rs):
                    relative_strength[sector]['rs_rank'] = i + 1
            
            return {
                'relative_strength': relative_strength,
                'strongest_sector': sorted_rs[0][0] if relative_strength else None,
                'weakest_sector': sorted_rs[-1][0] if relative_strength else None
            }
            
        except Exception as e:
            self.logger.error("Relative strength calculation failed", error=str(e))
            return {'relative_strength': {}}
    
    def _identify_leaders_laggards(self, sector_data: Dict[str, pd.DataFrame], 
                                 performance_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Identify sector leaders and laggards"""
        try:
            sector_metrics = performance_analysis.get('sector_metrics', {})
            
            if not sector_metrics:
                return {'leaders': [], 'laggards': [], 'momentum_sectors': []}
            
            # Sort by total return
            sorted_by_performance = sorted(sector_metrics.items(), 
                                         key=lambda x: x[1]['total_return'], 
                                         reverse=True)
            
            num_sectors = len(sorted_by_performance)
            top_tercile = max(1, num_sectors // 3)
            
            leaders = [sector for sector, _ in sorted_by_performance[:top_tercile]]
            laggards = [sector for sector, _ in sorted_by_performance[-top_tercile:]]
            
            # Identify momentum sectors (good recent performance + positive trend)
            momentum_sectors = []
            for sector, metrics in sector_metrics.items():
                recent_perf = metrics.get('recent_performance', {})
                if recent_perf.get('5d', 0) > 0 and recent_perf.get('20d', 0) > 0:
                    momentum_sectors.append(sector)
            
            # Calculate momentum scores
            momentum_scores = {}
            for sector in momentum_sectors:
                metrics = sector_metrics[sector]
                recent = metrics.get('recent_performance', {})
                
                # Weight recent performance
                score = (
                    recent.get('5d', 0) * 0.5 +
                    recent.get('10d', 0) * 0.3 +
                    recent.get('20d', 0) * 0.2
                )
                momentum_scores[sector] = score
            
            return {
                'leaders': leaders,
                'laggards': laggards,
                'momentum_sectors': momentum_sectors,
                'momentum_scores': momentum_scores,
                'analysis_date': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error("Leaders/laggards identification failed", error=str(e))
            return {'leaders': [], 'laggards': [], 'momentum_sectors': []}
    
    def _generate_allocation_recommendations(self, performance_analysis: Dict[str, Any],
                                           rotation_analysis: Dict[str, Any],
                                           relative_strength: Dict[str, Any]) -> Dict[str, Any]:
        """Generate sector allocation recommendations"""
        try:
            recommendations = {}
            
            # Base allocation (equal weight)
            num_sectors = len(self.sectors)
            base_weight = 1.0 / num_sectors if num_sectors > 0 else 0
            
            for sector in self.sectors.keys():
                recommendations[sector] = {
                    'base_weight': base_weight,
                    'recommended_weight': base_weight,
                    'recommendation': 'hold',
                    'confidence': 0.5
                }
            
            # Adjust based on performance
            sector_metrics = performance_analysis.get('sector_metrics', {})
            rs_data = relative_strength.get('relative_strength', {})
            
            for sector in self.sectors.keys():
                current_weight = base_weight
                confidence = 0.5
                recommendation = 'hold'
                
                # Performance adjustment
                if sector in sector_metrics:
                    percentile = sector_metrics[sector].get('percentile', 0.5)
                    if percentile > 0.75:  # Top quartile
                        current_weight *= 1.2
                        recommendation = 'overweight'
                        confidence = 0.7
                    elif percentile < 0.25:  # Bottom quartile
                        current_weight *= 0.8
                        recommendation = 'underweight'
                        confidence = 0.7
                
                # Relative strength adjustment
                if sector in rs_data:
                    rs_score = rs_data[sector].get('relative_strength_score', 0)
                    if rs_score > 0.01:  # Strong relative strength
                        current_weight *= 1.1
                        confidence = min(0.9, confidence + 0.1)
                    elif rs_score < -0.01:  # Weak relative strength
                        current_weight *= 0.9
                        confidence = min(0.9, confidence + 0.1)
                
                # Rotation pattern adjustment
                if rotation_analysis.get('rotation_detected', False):
                    leading_sectors = rotation_analysis.get('leading_sectors', [])
                    if sector in leading_sectors:
                        current_weight *= 1.15
                        recommendation = 'overweight'
                        confidence = min(0.95, confidence + 0.15)
                
                # Normalize weight
                current_weight = max(0.05, min(0.3, current_weight))  # 5% to 30% bounds
                
                recommendations[sector].update({
                    'recommended_weight': current_weight,
                    'recommendation': recommendation,
                    'confidence': confidence
                })
            
            # Normalize weights to sum to 1
            total_weight = sum(r['recommended_weight'] for r in recommendations.values())
            if total_weight > 0:
                for sector in recommendations:
                    recommendations[sector]['recommended_weight'] /= total_weight
            
            return {
                'sector_allocations': recommendations,
                'total_sectors': len(recommendations),
                'max_weight': max(r['recommended_weight'] for r in recommendations.values()) if recommendations else 0,
                'min_weight': min(r['recommended_weight'] for r in recommendations.values()) if recommendations else 0
            }
            
        except Exception as e:
            self.logger.error("Allocation recommendation generation failed", error=str(e))
            return {'sector_allocations': {}}
    
    def _calculate_sector_risk_factors(self, correlation_analysis: Dict[str, Any], 
                                     sector_data: Dict[str, pd.DataFrame]) -> List[str]:
        """Calculate sector-specific risk factors"""
        risk_factors = []
        
        try:
            # Correlation risk
            avg_correlation = correlation_analysis.get('average_correlation', 0)
            if avg_correlation > 0.7:
                risk_factors.append(f"High sector correlation ({avg_correlation:.2f}) reduces diversification")
            
            # Volatility risk
            high_vol_sectors = []
            for sector, data in sector_data.items():
                if not data.empty:
                    returns = data['price'].pct_change().dropna()
                    if len(returns) > 10:
                        vol = returns.std() * np.sqrt(252)
                        if vol > 0.3:  # 30% annualized volatility
                            high_vol_sectors.append(f"{sector} ({vol:.1%})")
            
            if high_vol_sectors:
                risk_factors.append(f"High volatility sectors: {', '.join(high_vol_sectors)}")
            
            # Concentration risk
            high_corr_pairs = correlation_analysis.get('high_correlation_pairs', [])
            if len(high_corr_pairs) > 3:
                risk_factors.append(f"Multiple highly correlated sector pairs ({len(high_corr_pairs)})")
            
            # Sector rotation risk
            diversification_score = correlation_analysis.get('diversification_score', 0)
            if diversification_score < 0.3:
                risk_factors.append("Low diversification score indicates sector concentration risk")
            
        except Exception as e:
            self.logger.error("Risk factor calculation failed", error=str(e))
            risk_factors.append("Risk assessment limited due to data issues")
        
        return risk_factors if risk_factors else ["No significant sector risks identified"]
    
    # Helper methods
    def _calculate_max_drawdown(self, prices: pd.Series) -> float:
        """Calculate maximum drawdown"""
        try:
            peak = prices.expanding().max()
            drawdown = (prices - peak) / peak
            return drawdown.min()
        except:
            return 0.0
    
    def _calculate_rotation_strength(self, relative_performance: Dict[str, float]) -> float:
        """Calculate strength of sector rotation"""
        try:
            if len(relative_performance) < 2:
                return 0.0
            
            values = list(relative_performance.values())
            return np.std(values) * 2  # Higher std indicates stronger rotation
        except:
            return 0.0
    
    def _calculate_confidence(self, sector_data: Dict[str, pd.DataFrame], 
                            performance_analysis: Dict[str, Any]) -> float:
        """Calculate overall analysis confidence"""
        try:
            # Data coverage
            valid_sectors = sum(1 for data in sector_data.values() if not data.empty)
            coverage_ratio = valid_sectors / len(self.sectors)
            
            # Data quality (based on number of data points)
            avg_data_points = np.mean([len(data) for data in sector_data.values() if not data.empty])
            quality_score = min(1.0, avg_data_points / 50)  # 50 days for full quality
            
            # Analysis completeness
            has_performance = bool(performance_analysis.get('sector_metrics'))
            completeness = 1.0 if has_performance else 0.5
            
            overall_confidence = (coverage_ratio * 0.4 + quality_score * 0.4 + completeness * 0.2)
            
            return min(0.95, max(0.3, overall_confidence))
            
        except:
            return 0.5
    
    def _assess_data_quality(self, sector_data: Dict[str, pd.DataFrame]) -> float:
        """Assess quality of sector data"""
        try:
            if not sector_data:
                return 0.0
            
            quality_scores = []
            
            for sector, data in sector_data.items():
                if data.empty:
                    quality_scores.append(0.0)
                    continue
                
                # Check completeness
                completeness = 1.0 - (data.isnull().sum().sum() / (len(data) * len(data.columns)))
                
                # Check data length
                length_score = min(1.0, len(data) / 60)  # 60 days for full score
                
                # Check for realistic values (prices should be positive)
                realistic_score = 1.0 if (data['price'] > 0).all() else 0.5
                
                sector_quality = (completeness + length_score + realistic_score) / 3
                quality_scores.append(sector_quality)
            
            return np.mean(quality_scores) if quality_scores else 0.0
            
        except:
            return 0.5
    
    def _generate_recommendations(self, performance_analysis: Dict[str, Any],
                                rotation_analysis: Dict[str, Any],
                                allocation_recs: Dict[str, Any],
                                leaders_laggards: Dict[str, Any]) -> List[str]:
        """Generate actionable sector recommendations"""
        recommendations = []
        
        try:
            # Performance-based recommendations
            best_performer = performance_analysis.get('best_performer')
            worst_performer = performance_analysis.get('worst_performer')
            
            if best_performer:
                recommendations.append(f"Consider overweighting {best_performer} (top performer)")
            
            if worst_performer:
                recommendations.append(f"Consider underweighting {worst_performer} (worst performer)")
            
            # Rotation-based recommendations
            if rotation_analysis.get('rotation_detected', False):
                pattern = rotation_analysis.get('pattern')
                leading_sectors = rotation_analysis.get('leading_sectors', [])
                
                if pattern and leading_sectors:
                    recommendations.append(f"Sector rotation detected ({pattern}): favor {', '.join(leading_sectors[:2])}")
            
            # Momentum recommendations
            momentum_sectors = leaders_laggards.get('momentum_sectors', [])
            if momentum_sectors:
                top_momentum = momentum_sectors[:2]  # Top 2
                recommendations.append(f"Momentum favors: {', '.join(top_momentum)}")
            
            # Diversification recommendations
            allocation_data = allocation_recs.get('sector_allocations', {})
            overweight_sectors = [sector for sector, data in allocation_data.items() 
                                if data.get('recommendation') == 'overweight']
            
            if overweight_sectors:
                recommendations.append(f"Recommended overweights: {', '.join(overweight_sectors[:3])}")
            
            # Risk management
            if not recommendations:
                recommendations.append("Maintain balanced sector allocation given mixed signals")
            
            recommendations.append("Monitor sector rotation patterns for allocation adjustments")
            
        except Exception as e:
            self.logger.error("Recommendation generation failed", error=str(e))
            recommendations.append("Maintain conservative sector allocation due to analysis limitations")
        
        return recommendations
    
    # Sample data generation methods
    def _load_sample_sector_data(self):
        """Load sample sector data for development"""
        self.sample_sector_data = {}
        for sector in self.sectors.keys():
            self.sample_sector_data[sector] = self._generate_sample_sector_price(sector)
    
    def _generate_sample_sector_price(self, sector: str) -> pd.DataFrame:
        """Generate sample price data for a sector"""
        days = self.config.get('lookback_period', 60)
        dates = pd.date_range(end=datetime.now(), periods=days, freq='D')
        
        # Different characteristics for different sectors
        base_price = 100
        
        if sector == 'technology':
            # Higher growth, higher volatility
            trend = 0.0008  # ~20% annual
            volatility = 0.02
        elif sector == 'utilities':
            # Lower growth, lower volatility
            trend = 0.0003  # ~8% annual
            volatility = 0.01
        elif sector == 'energy':
            # Cyclical, high volatility
            trend = 0.0002
            volatility = 0.025
            # Add cyclical pattern
            cycle = 0.05 * np.sin(2 * np.pi * np.arange(days) / 60)
        else:
            # Default moderate characteristics
            trend = 0.0005  # ~13% annual
            volatility = 0.015
        
        # Generate returns
        returns = np.random.normal(trend, volatility, days)
        
        # Add sector-specific patterns
        if sector == 'energy':
            returns += cycle
        elif sector == 'consumer_discretionary':
            # Add some seasonality
            seasonal = 0.01 * np.sin(2 * np.pi * np.arange(days) / 365)
            returns += seasonal
        
        # Calculate prices
        prices = base_price * np.cumprod(1 + returns)
        
        return pd.DataFrame({
            'price': prices
        }, index=dates)
    
    def _generate_sample_sector_data(self, symbols: List[str], lookback: int) -> Dict[str, pd.DataFrame]:
        """Generate sample data for multiple sectors"""
        sample_data = {}
        for sector in self.sectors.keys():
            sample_data[sector] = self._generate_sample_sector_price(sector)
        return sample_data
