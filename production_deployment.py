#!/usr/bin/env python3
"""
Dr. Apex Production Deployment Script
Deploy only validated, working components with comprehensive monitoring
"""

import os
import sys
import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Any

# Set up production logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('production_deployment.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def load_environment():
    """Load production environment variables"""
    logger.info("Loading production environment...")
    
    env_path = '.env'
    if not os.path.exists(env_path):
        logger.error("Environment file not found")
        return False
    
    with open(env_path, 'r') as f:
        for line in f:
            if '=' in line and not line.startswith('#'):
                key, value = line.strip().split('=', 1)
                os.environ[key] = value
    
    # Validate critical API keys
    required_keys = ['POLYGON_API_KEY', 'FRED_API_KEY']
    missing_keys = [key for key in required_keys if not os.getenv(key)]
    
    if missing_keys:
        logger.error(f"Missing required API keys: {missing_keys}")
        return False
    
    logger.info("✅ Environment loaded successfully")
    return True

class ProductionQuantitativeSystem:
    """Production-ready quantitative analysis system with only validated components"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.components = {}
        self.health_status = {}
        
    async def initialize(self):
        """Initialize production system with validated components only"""
        self.logger.info("Initializing production quantitative system...")
        
        try:
            # Add path for imports
            sys.path.insert(0, '/Users/<USER>/crypto')
            
            # Initialize working components only
            await self._initialize_quantitative_agent()
            await self._initialize_stock_forecasting()
            await self._initialize_orchestrator()
            
            self.logger.info("✅ Production system initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ System initialization failed: {e}")
            return False
    
    async def _initialize_quantitative_agent(self):
        """Initialize quantitative agent with production config"""
        try:
            from src.agents.quantitative import QuantitativeAgent
            
            # Production configuration
            config = {
                'lookback_period': 252,  # 1 year of data
                'min_observations': 50,  # Require sufficient data
                'confidence_level': 0.95,
                'risk_free_rate': 0.03,
                'factor_models': ['fama_french', 'momentum']
            }
            
            agent = QuantitativeAgent(config)
            await agent.initialize()
            
            self.components['quantitative_agent'] = agent
            self.health_status['quantitative_agent'] = 'operational'
            self.logger.info("✅ Quantitative agent initialized")
            
        except Exception as e:
            self.logger.error(f"❌ Quantitative agent initialization failed: {e}")
            self.health_status['quantitative_agent'] = 'failed'
            raise
    
    async def _initialize_stock_forecasting(self):
        """Initialize stock forecasting engine"""
        try:
            from src.orchestration.master_orchestrator import StockForecastingEngine
            
            engine = StockForecastingEngine()
            
            self.components['stock_forecasting'] = engine
            self.health_status['stock_forecasting'] = 'operational'
            self.logger.info("✅ Stock forecasting engine initialized")
            
        except Exception as e:
            self.logger.error(f"❌ Stock forecasting initialization failed: {e}")
            self.health_status['stock_forecasting'] = 'failed'
            raise
    
    async def _initialize_orchestrator(self):
        """Initialize master orchestrator components"""
        try:
            from src.orchestration.master_orchestrator import (
                BayesianSynthesizer, 
                BlackSwanDetector,
                NoiseManagementSystem
            )
            
            self.components['bayesian_synthesizer'] = BayesianSynthesizer()
            self.components['black_swan_detector'] = BlackSwanDetector()
            self.components['noise_manager'] = NoiseManagementSystem()
            
            self.health_status['orchestrator'] = 'operational'
            self.logger.info("✅ Master orchestrator components initialized")
            
        except Exception as e:
            self.logger.error(f"❌ Orchestrator initialization failed: {e}")
            self.health_status['orchestrator'] = 'failed'
            raise
    
    async def analyze_portfolio(self, symbols: List[str]) -> Dict[str, Any]:
        """Perform comprehensive portfolio analysis using working components"""
        self.logger.info(f"Starting portfolio analysis for {symbols}")
        
        try:
            results = {}
            
            # 1. Quantitative Analysis
            if 'quantitative_agent' in self.components:
                self.logger.info("Running quantitative analysis...")
                quant_result = await self.components['quantitative_agent'].analyze(symbols, '1d')
                results['quantitative'] = {
                    'confidence': quant_result.confidence,
                    'data_quality': quant_result.data_quality,
                    'recommendations': quant_result.recommendations,
                    'risk_factors': quant_result.risk_factors,
                    'predictions': quant_result.predictions
                }
                self.logger.info(f"✅ Quantitative analysis complete (confidence: {quant_result.confidence:.3f})")
            
            # 2. Individual Stock Forecasts
            if 'stock_forecasting' in self.components:
                self.logger.info("Generating stock forecasts...")
                forecasts = {}
                
                for symbol in symbols:
                    forecast = self.components['stock_forecasting'].generate_forecast(symbol, days=5)
                    if 'error' not in forecast:
                        forecasts[symbol] = forecast
                        self.logger.info(f"✅ {symbol} forecast: {forecast['expected_return']:.3f} return")
                    else:
                        self.logger.warning(f"❌ {symbol} forecast failed: {forecast['error']}")
                
                results['stock_forecasts'] = forecasts
            
            # 3. Risk Assessment
            if 'black_swan_detector' in self.components:
                self.logger.info("Assessing black swan risks...")
                
                # Create mock agent results for risk assessment
                mock_agent_results = {}
                if 'quantitative' in results:
                    from src.agents.base import AgentResult
                    mock_agent_results['quantitative'] = AgentResult(
                        agent_name='quantitative',
                        analysis_type='quantitative',
                        timestamp=datetime.now(),
                        symbols=symbols,
                        confidence=results['quantitative']['confidence'],
                        predictions=results['quantitative']['predictions'],
                        recommendations=results['quantitative']['recommendations'],
                        risk_factors=results['quantitative']['risk_factors'],
                        data_quality=results['quantitative']['data_quality']
                    )
                
                risks = self.components['black_swan_detector'].detect_black_swan_risks(mock_agent_results)
                results['black_swan_risks'] = risks
                self.logger.info(f"✅ Risk assessment complete ({len(risks)} risks identified)")
            
            # 4. Portfolio Summary
            results['portfolio_summary'] = self._generate_portfolio_summary(results, symbols)
            results['analysis_timestamp'] = datetime.now().isoformat()
            results['system_health'] = self.health_status
            
            self.logger.info("✅ Portfolio analysis complete")
            return results
            
        except Exception as e:
            self.logger.error(f"❌ Portfolio analysis failed: {e}")
            raise
    
    def _generate_portfolio_summary(self, results: Dict[str, Any], symbols: List[str]) -> Dict[str, Any]:
        """Generate executive summary of portfolio analysis"""
        summary = {
            'symbols_analyzed': symbols,
            'analysis_components': list(results.keys()),
            'overall_confidence': 0.0,
            'key_recommendations': [],
            'major_risks': [],
            'system_status': 'operational'
        }
        
        # Calculate overall confidence
        confidences = []
        if 'quantitative' in results:
            confidences.append(results['quantitative']['confidence'])
        
        if confidences:
            summary['overall_confidence'] = sum(confidences) / len(confidences)
        
        # Aggregate recommendations
        if 'quantitative' in results:
            summary['key_recommendations'].extend(results['quantitative']['recommendations'])
        
        # Aggregate risks
        if 'quantitative' in results:
            summary['major_risks'].extend(results['quantitative']['risk_factors'])
        
        if 'black_swan_risks' in results:
            for risk in results['black_swan_risks'][:3]:  # Top 3 risks
                summary['major_risks'].append(risk.get('description', 'Unknown risk'))
        
        return summary
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get current system health status"""
        return {
            'timestamp': datetime.now().isoformat(),
            'components': self.health_status,
            'overall_status': 'operational' if all(
                status == 'operational' for status in self.health_status.values()
            ) else 'degraded'
        }

async def main():
    """Main production deployment function"""
    logger.info("🚀 Dr. Apex Production Deployment Starting...")
    
    # Load environment
    if not load_environment():
        logger.error("❌ Environment setup failed")
        return False
    
    # Initialize production system
    system = ProductionQuantitativeSystem()
    if not await system.initialize():
        logger.error("❌ System initialization failed")
        return False
    
    # Test with sample portfolio
    test_symbols = ['AAPL', 'MSFT', 'GOOGL']
    logger.info(f"🧪 Testing with sample portfolio: {test_symbols}")
    
    try:
        results = await system.analyze_portfolio(test_symbols)
        
        # Save results
        output_file = f"production_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"✅ Analysis results saved to {output_file}")
        
        # Print summary
        summary = results.get('portfolio_summary', {})
        logger.info("📊 Production Analysis Summary:")
        logger.info(f"   Overall Confidence: {summary.get('overall_confidence', 0):.3f}")
        logger.info(f"   Components Used: {len(summary.get('analysis_components', []))}")
        logger.info(f"   Recommendations: {len(summary.get('key_recommendations', []))}")
        logger.info(f"   Risks Identified: {len(summary.get('major_risks', []))}")
        
        # System health check
        health = system.get_system_health()
        logger.info(f"🏥 System Health: {health['overall_status'].upper()}")
        
        logger.info("✅ Production deployment test successful")
        return True
        
    except Exception as e:
        logger.error(f"❌ Production test failed: {e}")
        return False

if __name__ == '__main__':
    success = asyncio.run(main())
    if success:
        print("\n🎉 PRODUCTION SYSTEM READY FOR DEPLOYMENT")
        print("📋 Components: Quantitative Agent, Stock Forecasting, Risk Assessment")
        print("⚠️  Note: Technical Analysis Agent excluded (requires rebuild)")
    else:
        print("\n❌ PRODUCTION DEPLOYMENT FAILED")
        print("🔧 Check logs for details")
    
    sys.exit(0 if success else 1)
