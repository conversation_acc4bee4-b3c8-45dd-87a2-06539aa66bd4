#!/usr/bin/env python3
"""
Main Integration Script
Demonstrates the enhanced AI system with modular agents
"""

import asyncio
import os
import sys
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.core.system import credentials, health_monitor, logger
from src.data.sources import DataSourceManager
from src.agents import (
    MacroeconomicAgent, SectorAnalysisAgent, 
    EnhancedQuantitativeAgent, EnhancedTechnicalAnalysisAgent
)
from src.orchestration.master_orchestrator import MasterOrchestrator

async def demonstrate_enhanced_system():
    """Demonstrate the enhanced AI system capabilities"""
    try:
        logger.info("Starting Enhanced Market Analysis System Demo")
        
        # Initialize data source manager
        data_manager = DataSourceManager(credentials)
        
        # Initialize enhanced agents
        macro_agent = MacroeconomicAgent("macro_001", credentials)
        sector_agent = SectorAnalysisAgent("sector_001", credentials)
        quant_agent = EnhancedQuantitativeAgent("enhanced_quant_001", credentials)
        tech_agent = EnhancedTechnicalAnalysisAgent("enhanced_tech_001", credentials)
        
        # Initialize master orchestrator
        orchestrator = MasterOrchestrator(
            agents=[macro_agent, sector_agent, quant_agent, tech_agent],
            data_manager=data_manager
        )
        
        # Test symbols
        test_symbols = ["AAPL", "MSFT", "GOOGL"]
        
        logger.info(f"Testing system with symbols: {test_symbols}")
        
        # Run comprehensive analysis for each symbol
        for symbol in test_symbols:
            logger.info(f"Analyzing {symbol}")
            
            try:
                # Get market data
                end_date = datetime.now()
                start_date = end_date - timedelta(days=252)  # 1 year
                
                market_data = await data_manager.get_stock_data(
                    symbol, start_date, end_date
                )
                
                if market_data is None or market_data.empty:
                    logger.warning(f"No market data available for {symbol}")
                    continue
                
                logger.info(f"Retrieved {len(market_data)} days of data for {symbol}")
                
                # Test Enhanced Technical Agent
                logger.info(f"Running enhanced technical analysis for {symbol}")
                tech_config = {
                    'timeframes': ['1D', '1W'],
                    'max_retries': 3,
                    'enable_pattern_recognition': True,
                    'advanced_indicators': True
                }
                
                tech_result = await tech_agent.analyze(symbol, market_data, tech_config)
                
                if tech_result:
                    logger.info(f"Technical analysis completed for {symbol}")
                    logger.info(f"Technical confidence: {tech_result.confidence:.2f}")
                    logger.info(f"Technical prediction: {tech_result.prediction}")
                else:
                    logger.warning(f"Technical analysis failed for {symbol}")
                
                # Test Enhanced Quantitative Agent
                logger.info(f"Running enhanced quantitative analysis for {symbol}")
                quant_config = {
                    'enable_garch': True,
                    'enable_neural_network': True,
                    'monte_carlo_simulations': 1000,
                    'risk_metrics': ['VaR', 'CVaR', 'Sharpe']
                }
                
                quant_result = await quant_agent.analyze(symbol, market_data, quant_config)
                
                if quant_result:
                    logger.info(f"Quantitative analysis completed for {symbol}")
                    logger.info(f"Quantitative confidence: {quant_result.confidence:.2f}")
                    logger.info(f"Quantitative prediction: {quant_result.prediction}")
                else:
                    logger.warning(f"Quantitative analysis failed for {symbol}")
                
                # Test Master Orchestrator
                logger.info(f"Running master orchestration for {symbol}")
                
                orchestration_result = await orchestrator.comprehensive_analysis(symbol)
                
                if orchestration_result:
                    logger.info(f"Master orchestration completed for {symbol}")
                    logger.info(f"Final recommendation: {orchestration_result.get('recommendation', 'N/A')}")
                    logger.info(f"Overall confidence: {orchestration_result.get('confidence', 'N/A')}")
                else:
                    logger.warning(f"Master orchestration failed for {symbol}")
                
                # Display system health
                health_status = health_monitor.get_health_status()
                logger.info(f"System health: {health_status['status']} (Success rate: {health_status['success_rate']:.2%})")
                
            except Exception as e:
                logger.error(f"Error analyzing {symbol}: {str(e)}")
                continue
        
        logger.info("Enhanced Market Analysis System Demo Completed")
        
    except Exception as e:
        logger.error(f"System demo failed: {str(e)}")
        raise

async def test_individual_agents():
    """Test individual agent capabilities"""
    logger.info("Testing individual agent capabilities")
    
    try:
        # Test Enhanced Technical Agent standalone
        tech_agent = EnhancedTechnicalAnalysisAgent("test_tech", credentials)
        
        # Create sample data for testing
        import pandas as pd
        import numpy as np
        
        dates = pd.date_range(start='2023-01-01', end='2024-01-01', freq='D')
        sample_data = pd.DataFrame({
            'Date': dates,
            'Open': 100 + np.cumsum(np.random.randn(len(dates)) * 0.5),
            'High': 0,
            'Low': 0,
            'Close': 0,
            'Volume': np.random.randint(1000000, 10000000, len(dates))
        })
        
        # Generate realistic OHLC data
        for i in range(len(sample_data)):
            open_price = sample_data.iloc[i]['Open']
            daily_range = abs(np.random.randn() * 2)
            sample_data.iloc[i, sample_data.columns.get_loc('High')] = open_price + daily_range
            sample_data.iloc[i, sample_data.columns.get_loc('Low')] = open_price - daily_range
            sample_data.iloc[i, sample_data.columns.get_loc('Close')] = open_price + np.random.randn() * 0.5
        
        sample_data.set_index('Date', inplace=True)
        
        logger.info("Testing technical agent with sample data")
        tech_result = await tech_agent.analyze("TEST", sample_data)
        
        if tech_result:
            logger.info("Technical agent test successful")
            logger.info(f"Test result confidence: {tech_result.confidence}")
        else:
            logger.warning("Technical agent test failed")
        
        # Test Enhanced Quantitative Agent standalone
        quant_agent = EnhancedQuantitativeAgent("test_quant", credentials)
        
        logger.info("Testing quantitative agent with sample data")
        quant_result = await quant_agent.analyze("TEST", sample_data)
        
        if quant_result:
            logger.info("Quantitative agent test successful")
            logger.info(f"Test result confidence: {quant_result.confidence}")
        else:
            logger.warning("Quantitative agent test failed")
        
    except Exception as e:
        logger.error(f"Individual agent test failed: {str(e)}")

def main():
    """Main entry point"""
    logging.basicConfig(level=logging.INFO)
    
    try:
        # Check if required environment variables are set
        required_vars = ['OPENROUTER_API_KEY']
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        
        if missing_vars:
            logger.warning(f"Missing environment variables: {missing_vars}")
            logger.info("Some features may not work without proper API keys")
        
        # Run tests
        asyncio.run(test_individual_agents())
        
        # Run full demo if API keys are available
        if not missing_vars:
            asyncio.run(demonstrate_enhanced_system())
        else:
            logger.info("Skipping full demo due to missing API keys")
            
    except KeyboardInterrupt:
        logger.info("Demo interrupted by user")
    except Exception as e:
        logger.error(f"Demo failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
