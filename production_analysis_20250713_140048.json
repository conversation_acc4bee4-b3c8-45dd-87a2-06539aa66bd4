{"quantitative": {"confidence": 0.6096753587200873, "data_quality": 1.0, "recommendations": ["Strong factor exposure to basic model (R²: 0.60)", "Low diversification detected - consider broader allocation", "Monitor factor exposures and rebalance based on signal strength"], "risk_factors": ["No significant quantitative risks identified"], "predictions": {"factor_analysis": {"factor_loadings": {"basic": {"AAPL": {"alpha": 0.3980802517904488, "betas": {"market": 1.0}, "r_squared": 0.6, "tracking_error": 0.3096389776658483}, "MSFT": {"alpha": 0.14767029249117294, "betas": {"market": 1.0}, "r_squared": 0.6, "tracking_error": 0.30448731880822616}, "GOOGL": {"alpha": -0.32644323413444576, "betas": {"market": 1.0}, "r_squared": 0.6, "tracking_error": 0.3019879927977712}}}, "factor_scores": {"basic": {"AAPL": {"market": 1.0}, "MSFT": {"market": 1.0}, "GOOGL": {"market": 1.0}}}, "factor_exposures": {"basic": {"AAPL": {"market": 1.0}, "MSFT": {"market": 1.0}, "GOOGL": {"market": 1.0}}}, "factor_summary": {"basic": {"model_type": "basic", "factors": ["market"]}}, "analysis_period": 3}, "risk_model": {"covariance_matrix": {}, "volatilities": {}}, "strategy_signals": {"individual_strategies": {"mean_reversion": {"strategy": "mean_reversion", "signals": {}}, "momentum": {"strategy": "momentum", "signals": {}}, "pairs_trading": {"strategy": "pairs_trading", "signals": {}}, "risk_parity": {"strategy": "risk_parity", "signals": {}}}, "aggregated_signals": {"AAPL": {"combined_signal": 0.0, "confidence": 0.5}, "MSFT": {"combined_signal": 0.0, "confidence": 0.5}, "GOOGL": {"combined_signal": 0.0, "confidence": 0.5}}, "signal_strength": 0.0}, "ml_predictions": {"individual_models": {"AAPL": {"random_forest": {"forecast": -0.01766916640466519, "mse": 0.000535348449967239, "mae": 0.01950362630908252, "correlation": -0.035543749145093735, "confidence": 0.035543749145093735}, "gradient_boosting": {"forecast": -0.019084175693983595, "mse": 0.0006249788012542566, "mae": 0.02101800920748173, "correlation": -0.05022079098179396, "confidence": 0.05022079098179396}}, "MSFT": {"random_forest": {"forecast": -0.0009943681517649416, "mse": 0.00033079247167662186, "mae": 0.015314960278925475, "correlation": 0.2559515024333036, "confidence": 0.2559515024333036}, "gradient_boosting": {"forecast": 0.0006547998510543255, "mse": 0.00042434105009919347, "mae": 0.01707715256675911, "correlation": 0.23693030830941852, "confidence": 0.23693030830941852}}, "GOOGL": {"random_forest": {"forecast": 0.0060710425973694755, "mse": 0.0005708431945344795, "mae": 0.019610705703072367, "correlation": 0.0003111737581097707, "confidence": 0.0003111737581097707}, "gradient_boosting": {"forecast": 0.008020000512127388, "mse": 0.0006963235507366339, "mae": 0.02211310738706712, "correlation": -0.014549649774025415, "confidence": 0.014549649774025415}}}, "ensemble_predictions": {"AAPL": {"forecast": -0.01849774760676829, "confidence": 0.04288227006344385}, "MSFT": {"forecast": -0.00020160632764082707, "confidence": 0.24644090537136107}, "GOOGL": {"forecast": 0.00797919089229565, "confidence": 0.007430411766067593}}, "model_performance": {"AAPL": {"random_forest": {"mse": 0.000535348449967239, "mae": 0.01950362630908252, "correlation": -0.035543749145093735}, "gradient_boosting": {"mse": 0.0006249788012542566, "mae": 0.02101800920748173, "correlation": -0.05022079098179396}}, "MSFT": {"random_forest": {"mse": 0.00033079247167662186, "mae": 0.015314960278925475, "correlation": 0.2559515024333036}, "gradient_boosting": {"mse": 0.00042434105009919347, "mae": 0.01707715256675911, "correlation": 0.23693030830941852}}, "GOOGL": {"random_forest": {"mse": 0.0005708431945344795, "mae": 0.019610705703072367, "correlation": 0.0003111737581097707}, "gradient_boosting": {"mse": 0.0006963235507366339, "mae": 0.02211310738706712, "correlation": -0.014549649774025415}}}}, "portfolio_optimization": {"optimal_weights": {}, "expected_return": 0, "expected_risk": 0}, "performance_attribution": {"factor_attribution": {}, "specific_returns": {}, "attribution_summary": {}}, "risk_metrics": {"var_es": {}, "diversification_ratio": 0, "maximum_drawdown": 0.0, "sharpe_ratio": 0.0, "sortino_ratio": 0.0}, "forecasts": {"price_forecasts": {"AAPL": {"forecast": -0.01849774760676829, "confidence": 0.04288227006344385}, "MSFT": {"forecast": -0.00020160632764082707, "confidence": 0.24644090537136107}, "GOOGL": {"forecast": 0.00797919089229565, "confidence": 0.007430411766067593}}, "volatility_forecasts": {}, "correlation_forecasts": {}, "forecast_horizon": 1}}}, "stock_forecasts": {"AAPL": {"symbol": "AAPL", "current_price": 211.16000366210938, "target_price": 207.01956351085877, "expected_return": -0.019608070086397554, "confidence": 0.4947259133873628, "signals": {"momentum": -0.021033917848045473, "reversion": -0.06622320425117713, "fundamental": 0.0, "volatility_adj": 0.8988639375064721, "volume_confirmation": 0.3635906102626265, "trend_strength": 0.6989039486053069}, "timeframe_days": 5, "timestamp": "2025-07-13T14:00:47.858602"}, "MSFT": {"symbol": "MSFT", "current_price": 503.32000732421875, "target_price": 489.64356028383236, "expected_return": -0.02717246849195203, "confidence": 0.4325862187932884, "signals": {"momentum": -0.02427982923232877, "reversion": -0.09347926909491683, "fundamental": 0.0, "volatility_adj": 0.9229849371448596, "volume_confirmation": 0.41267037229864545, "trend_strength": 0.7199174389485582}, "timeframe_days": 5, "timestamp": "2025-07-13T14:00:48.044065"}, "GOOGL": {"symbol": "GOOGL", "current_price": 180.19000244140625, "target_price": 176.04760982867506, "expected_return": -0.02298902578725591, "confidence": 0.42272808807142415, "signals": {"momentum": -0.020212982411458733, "reversion": -0.08678492644881411, "fundamental": 0.0, "volatility_adj": 0.859419629117312, "volume_confirmation": 0.42272294266820015, "trend_strength": 0.5070054834856891}, "timeframe_days": 5, "timestamp": "2025-07-13T14:00:48.134147"}}, "black_swan_risks": [], "portfolio_summary": {"symbols_analyzed": ["AAPL", "MSFT", "GOOGL"], "analysis_components": ["quantitative", "stock_forecasts", "black_swan_risks"], "overall_confidence": 0.6096753587200873, "key_recommendations": ["Strong factor exposure to basic model (R²: 0.60)", "Low diversification detected - consider broader allocation", "Monitor factor exposures and rebalance based on signal strength"], "major_risks": ["No significant quantitative risks identified"], "system_status": "operational"}, "analysis_timestamp": "2025-07-13T14:00:48.134347", "system_health": {"quantitative_agent": "operational", "stock_forecasting": "operational", "orchestrator": "operational"}}