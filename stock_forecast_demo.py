#!/usr/bin/env python3
"""
Stock-Focused Reliable Forecasting System Demo
$100 Budget - Free Data Sources Only
Focused on noise management and reliable signals
"""

import asyncio
import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Simple version for demonstration
class StockForecastDemo:
    """Simplified stock forecasting with noise management"""
    
    def __init__(self):
        self.noise_thresholds = {
            'volume_spike': 3.0,
            'price_spike': 2.5,
            'volatility_break': 2.0
        }
    
    def fetch_stock_data(self, symbol: str, period: str = "6mo") -> pd.DataFrame:
        """Fetch stock data using free yfinance"""
        try:
            ticker = yf.Ticker(symbol)
            data = ticker.history(period=period)
            
            if data.empty:
                raise ValueError(f"No data for {symbol}")
            
            # Add technical indicators
            data['returns'] = data['Close'].pct_change()
            data['sma_20'] = data['Close'].rolling(20).mean()
            data['sma_50'] = data['Close'].rolling(50).mean()
            data['volatility'] = data['returns'].rolling(20).std()
            data['rsi'] = self._calculate_rsi(data['Close'])
            
            return data
            
        except Exception as e:
            print(f"Error fetching data for {symbol}: {e}")
            return pd.DataFrame()
    
    def filter_noise(self, data: pd.DataFrame, symbol: str) -> dict:
        """Apply noise filtering with detailed metrics"""
        try:
            noise_flags = {}
            
            # 1. Volume spike detection
            volume_z = (data['Volume'] - data['Volume'].rolling(20).mean()) / data['Volume'].rolling(20).std()
            volume_spikes = (abs(volume_z) > self.noise_thresholds['volume_spike']).sum()
            noise_flags['volume_spikes'] = volume_spikes
            
            # 2. Price spike detection  
            return_z = (data['returns'] - data['returns'].mean()) / data['returns'].std()
            price_spikes = (abs(return_z) > self.noise_thresholds['price_spike']).sum()
            noise_flags['price_spikes'] = price_spikes
            
            # 3. Volatility regime breaks
            vol_z = (data['volatility'] - data['volatility'].mean()) / data['volatility'].std()
            vol_breaks = (abs(vol_z) > self.noise_thresholds['volatility_break']).sum()
            noise_flags['volatility_breaks'] = vol_breaks
            
            # Calculate data quality
            total_obs = len(data)
            total_noise = sum(noise_flags.values())
            noise_score = total_noise / total_obs if total_obs > 0 else 0
            data_quality = 1.0 - noise_score
            
            return {
                'data_quality': data_quality,
                'noise_score': noise_score,
                'noise_flags': noise_flags,
                'usable_observations': total_obs - total_noise
            }
            
        except Exception as e:
            print(f"Noise filtering failed for {symbol}: {e}")
            return {'data_quality': 0.5, 'noise_score': 0.5, 'noise_flags': {}}
    
    def generate_forecast(self, data: pd.DataFrame, symbol: str) -> dict:
        """Generate multi-timeframe forecast"""
        try:
            current_price = data['Close'].iloc[-1]
            
            forecasts = {}
            for timeframe in [1, 5, 21]:  # 1d, 5d, 21d
                forecast = self._forecast_timeframe(data, timeframe)
                forecasts[f'{timeframe}d'] = forecast
            
            # Calculate consensus
            returns = [f['expected_return'] for f in forecasts.values()]
            confidences = [f['confidence'] for f in forecasts.values()]
            
            consensus_return = np.average(returns, weights=confidences)
            consensus_confidence = np.mean(confidences)
            
            # Calculate target prices
            for period, forecast in forecasts.items():
                forecast['target_price'] = current_price * (1 + forecast['expected_return'])
            
            return {
                'symbol': symbol,
                'current_price': current_price,
                'forecasts': forecasts,
                'consensus': {
                    'expected_return': consensus_return,
                    'confidence': consensus_confidence,
                    'target_price': current_price * (1 + consensus_return)
                },
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            print(f"Forecast generation failed for {symbol}: {e}")
            return self._default_forecast(symbol, data['Close'].iloc[-1] if not data.empty else 100)
    
    def _forecast_timeframe(self, data: pd.DataFrame, days: int) -> dict:
        """Generate forecast for specific timeframe"""
        try:
            # Technical signals
            momentum_signal = self._momentum_signal(data, days)
            reversion_signal = self._reversion_signal(data, days)
            volatility_adj = self._volatility_adjustment(data)
            
            # Combine signals
            combined_signal = (momentum_signal * 0.5 + reversion_signal * 0.5) * volatility_adj
            
            # Calculate confidence based on signal agreement
            signal_strength = abs(combined_signal)
            confidence = min(0.9, max(0.3, signal_strength * 2))  # Scale to 0.3-0.9
            
            return {
                'expected_return': combined_signal,
                'confidence': confidence,
                'components': {
                    'momentum': momentum_signal,
                    'reversion': reversion_signal,
                    'volatility_adj': volatility_adj
                }
            }
            
        except Exception:
            return {'expected_return': 0.0, 'confidence': 0.3, 'components': {}}
    
    def _momentum_signal(self, data: pd.DataFrame, days: int) -> float:
        """Calculate momentum signal"""
        try:
            # Price momentum
            price_mom = (data['Close'].iloc[-1] - data['Close'].iloc[-days]) / data['Close'].iloc[-days]
            
            # Moving average crossover
            ma_signal = 1 if data['sma_20'].iloc[-1] > data['sma_50'].iloc[-1] else -1
            ma_strength = abs(data['sma_20'].iloc[-1] - data['sma_50'].iloc[-1]) / data['sma_50'].iloc[-1]
            ma_signal *= min(ma_strength, 0.05)  # Cap at 5%
            
            # RSI momentum
            rsi = data['rsi'].iloc[-1]
            rsi_signal = (50 - rsi) / 100 * 0.03  # Max 3% from RSI
            
            # Combine momentum signals
            momentum = price_mom * 0.5 + ma_signal * 0.3 + rsi_signal * 0.2
            
            # Scale by timeframe
            return momentum * np.sqrt(days / 21)
            
        except Exception:
            return 0.0
    
    def _reversion_signal(self, data: pd.DataFrame, days: int) -> float:
        """Calculate mean reversion signal"""
        try:
            current_price = data['Close'].iloc[-1]
            
            # Deviation from moving averages
            sma20_dev = (data['sma_20'].iloc[-1] - current_price) / current_price
            sma50_dev = (data['sma_50'].iloc[-1] - current_price) / current_price
            
            # Bollinger band position
            bb_upper = data['sma_20'].iloc[-1] + 2 * data['Close'].rolling(20).std().iloc[-1]
            bb_lower = data['sma_20'].iloc[-1] - 2 * data['Close'].rolling(20).std().iloc[-1]
            bb_position = (current_price - bb_lower) / (bb_upper - bb_lower)
            bb_signal = (0.5 - bb_position) * 0.1  # Max 5% signal
            
            # Combine reversion signals
            reversion = sma20_dev * 0.4 + sma50_dev * 0.4 + bb_signal * 0.2
            
            # Reversion works better short-term
            return reversion * np.sqrt(21 / max(days, 1))
            
        except Exception:
            return 0.0
    
    def _volatility_adjustment(self, data: pd.DataFrame) -> float:
        """Adjust signal based on volatility"""
        try:
            current_vol = data['volatility'].iloc[-1]
            vol_percentile = (current_vol > data['volatility']).mean()
            
            # Lower signals in high volatility
            if vol_percentile > 0.8:
                return 0.5  # High vol - reduce signals
            elif vol_percentile < 0.2:
                return 1.0  # Low vol - full signals
            else:
                return 0.8  # Medium vol - moderate reduction
                
        except Exception:
            return 0.7
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI indicator"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        except Exception:
            return pd.Series(50, index=prices.index)  # Neutral RSI
    
    def _default_forecast(self, symbol: str, price: float) -> dict:
        """Default forecast when analysis fails"""
        return {
            'symbol': symbol,
            'current_price': price,
            'forecasts': {
                '1d': {'expected_return': 0.0, 'confidence': 0.3, 'target_price': price},
                '5d': {'expected_return': 0.0, 'confidence': 0.3, 'target_price': price},
                '21d': {'expected_return': 0.0, 'confidence': 0.3, 'target_price': price}
            },
            'consensus': {'expected_return': 0.0, 'confidence': 0.3, 'target_price': price},
            'timestamp': datetime.now(),
            'status': 'fallback'
        }
    
    def analyze_portfolio(self, symbols: list) -> dict:
        """Analyze a portfolio of stocks"""
        results = {}
        portfolio_summary = {
            'total_symbols': len(symbols),
            'successful_analysis': 0,
            'average_confidence': 0.0,
            'high_confidence_picks': [],
            'risk_assessment': 'UNKNOWN'
        }
        
        print(f"\n🎯 ANALYZING PORTFOLIO: {symbols}")
        print("=" * 60)
        
        for symbol in symbols:
            try:
                print(f"\n📊 Analyzing {symbol}...")
                
                # Fetch data
                data = self.fetch_stock_data(symbol)
                if data.empty:
                    print(f"❌ No data available for {symbol}")
                    continue
                
                # Filter noise
                noise_analysis = self.filter_noise(data, symbol)
                print(f"   Data Quality: {noise_analysis['data_quality']:.1%}")
                
                # Generate forecast
                forecast = self.generate_forecast(data, symbol)
                
                # Store results
                results[symbol] = {
                    'forecast': forecast,
                    'noise_analysis': noise_analysis,
                    'data_quality': noise_analysis['data_quality']
                }
                
                # Update summary
                portfolio_summary['successful_analysis'] += 1
                confidence = forecast['consensus']['confidence']
                portfolio_summary['average_confidence'] += confidence
                
                if confidence > 0.7:
                    portfolio_summary['high_confidence_picks'].append(symbol)
                
                # Display key metrics
                consensus = forecast['consensus']
                print(f"   Expected Return: {consensus['expected_return']:+.1%}")
                print(f"   Confidence: {consensus['confidence']:.1%}")
                print(f"   Target Price: ${consensus['target_price']:.2f}")
                
            except Exception as e:
                print(f"❌ Analysis failed for {symbol}: {e}")
                continue
        
        # Finalize portfolio summary
        if portfolio_summary['successful_analysis'] > 0:
            portfolio_summary['average_confidence'] /= portfolio_summary['successful_analysis']
            
            # Risk assessment
            if portfolio_summary['average_confidence'] > 0.7:
                portfolio_summary['risk_assessment'] = 'LOW'
            elif portfolio_summary['average_confidence'] > 0.5:
                portfolio_summary['risk_assessment'] = 'MEDIUM'
            else:
                portfolio_summary['risk_assessment'] = 'HIGH'
        
        return {
            'individual_analysis': results,
            'portfolio_summary': portfolio_summary,
            'timestamp': datetime.now()
        }

def main():
    """Demo the stock forecasting system"""
    print("🚀 STOCK-FOCUSED RELIABLE FORECASTING SYSTEM")
    print("💰 $100 Budget - Free Data Sources Only")
    print("🎯 Focus: Noise Management & Reliable Signals")
    print("=" * 60)
    
    # Initialize system
    forecaster = StockForecastDemo()
    
    # Demo portfolio (mix of large caps for reliability)
    demo_symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA']
    
    # Run analysis
    try:
        portfolio_analysis = forecaster.analyze_portfolio(demo_symbols)
        
        # Display portfolio summary
        summary = portfolio_analysis['portfolio_summary']
        print(f"\n📈 PORTFOLIO SUMMARY")
        print("=" * 40)
        print(f"Symbols Analyzed: {summary['successful_analysis']}/{summary['total_symbols']}")
        print(f"Average Confidence: {summary['average_confidence']:.1%}")
        print(f"High Confidence Picks: {len(summary['high_confidence_picks'])}")
        print(f"Risk Assessment: {summary['risk_assessment']}")
        
        if summary['high_confidence_picks']:
            print(f"Top Picks: {', '.join(summary['high_confidence_picks'])}")
        
        # Display individual forecasts
        print(f"\n📊 INDIVIDUAL FORECASTS")
        print("=" * 40)
        for symbol, analysis in portfolio_analysis['individual_analysis'].items():
            forecast = analysis['forecast']
            consensus = forecast['consensus']
            data_quality = analysis['data_quality']
            
            print(f"\n{symbol}:")
            print(f"  Current: ${forecast['current_price']:.2f}")
            print(f"  Target:  ${consensus['target_price']:.2f} ({consensus['expected_return']:+.1%})")
            print(f"  Confidence: {consensus['confidence']:.1%}")
            print(f"  Data Quality: {data_quality:.1%}")
            
            # Timeframe breakdown
            print("  Timeframe Forecasts:")
            for period, tf_forecast in forecast['forecasts'].items():
                print(f"    {period}: {tf_forecast['expected_return']:+.1%} (conf: {tf_forecast['confidence']:.1%})")
        
        print(f"\n✅ ANALYSIS COMPLETE")
        print(f"Total Cost: $0 (Free data sources)")
        print(f"Analysis Time: ~30 seconds")
        print(f"Reliability Focus: Noise filtering & confidence scoring")
        
    except Exception as e:
        print(f"❌ Portfolio analysis failed: {e}")

if __name__ == "__main__":
    main()
