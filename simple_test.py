#!/usr/bin/env python3
"""
Simple Integration Test for Enhanced Agents
Tests basic functionality with proper configuration
"""

import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.agents.technical import EnhancedTechnicalAnalysisAgent
from src.agents.enhanced_quantitative import EnhancedQuantitativeAgent
from src.core.system import logger

def create_sample_data(symbol: str = "TEST", periods: int = 100) -> pd.DataFrame:
    """Create realistic sample OHLC data for testing"""
    dates = pd.date_range(start=datetime.now() - timedelta(days=periods), 
                         end=datetime.now(), freq='D')
    
    # Generate realistic price movement
    np.random.seed(42)
    base_price = 100.0
    returns = np.random.normal(0.0005, 0.02, len(dates))
    
    # Add some autocorrelation
    for i in range(1, len(returns)):
        returns[i] += 0.1 * returns[i-1]
    
    prices = base_price * np.exp(np.cumsum(returns))
    
    # Create OHLC data
    ohlc_data = []
    
    for i, price in enumerate(prices):
        daily_range = price * np.random.uniform(0.005, 0.03)
        
        if i == 0:
            open_price = price
        else:
            gap = np.random.normal(0, 0.002) * price
            open_price = prices[i-1] + gap
        
        high_price = max(open_price, price) + np.random.uniform(0, daily_range)
        low_price = min(open_price, price) - np.random.uniform(0, daily_range)
        volume = np.random.randint(1_000_000, 10_000_000)
        
        ohlc_data.append({
            'open': round(open_price, 2),
            'high': round(high_price, 2),
            'low': round(low_price, 2),
            'close': round(price, 2),
            'volume': volume
        })
    
    data = pd.DataFrame(ohlc_data, index=dates)
    
    return data

async def test_enhanced_technical_agent_simple():
    """Test the enhanced technical analysis agent with proper config"""
    print("\\n" + "="*50)
    print("TESTING ENHANCED TECHNICAL AGENT (SIMPLE)")
    print("="*50)
    
    try:
        # Create agent with proper configuration
        config = {
            'lookback_period': 100,
            'min_pattern_strength': 0.7,
            'signal_threshold': 0.6,
            'risk_tolerance': 0.02,
            'timeframes': ['1D'],
            'enable_pattern_recognition': True,
            'advanced_indicators': True
        }
        
        agent = EnhancedTechnicalAnalysisAgent(config)
        
        # Initialize agent
        await agent.initialize()
        print("✅ Agent initialized successfully")
        
        # Create sample data
        sample_data = create_sample_data("AAPL", 100)
        print(f"✅ Created sample data: {len(sample_data)} days")
        
        # Test analyze method
        result = await agent.analyze(["AAPL"], "1d", market_data=sample_data)
        
        if result:
            print(f"✅ Analysis successful!")
            print(f"   Agent: {result.agent_name}")
            print(f"   Confidence: {result.confidence:.2f}")
            print(f"   Predictions: {len(result.prediction)} items")
            print(f"   Risk factors: {len(result.risk_factors)} identified")
            print(f"   Recommendations: {len(result.recommendations)} provided")
            return True
        else:
            print("❌ Analysis returned None")
            return False
            
    except Exception as e:
        print(f"❌ Technical agent test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_enhanced_quantitative_agent_simple():
    """Test the enhanced quantitative analysis agent with proper config"""
    print("\\n" + "="*50)
    print("TESTING ENHANCED QUANTITATIVE AGENT (SIMPLE)")
    print("="*50)
    
    try:
        # Create agent with proper configuration
        config = {
            'enable_garch': False,  # Disable complex models for simple test
            'enable_neural_network': False,
            'monte_carlo_simulations': 100,  # Reduced for speed
            'risk_metrics': ['VaR', 'Sharpe']
        }
        
        agent = EnhancedQuantitativeAgent(config)
        
        # Initialize agent
        await agent.initialize()
        print("✅ Agent initialized successfully")
        
        # Create sample data
        sample_data = create_sample_data("MSFT", 100)
        print(f"✅ Created sample data: {len(sample_data)} days")
        
        # Test analyze method
        result = await agent.analyze(["MSFT"], "1d", market_data=sample_data)
        
        if result:
            print(f"✅ Analysis successful!")
            print(f"   Agent: {result.agent_name}")
            print(f"   Confidence: {result.confidence:.2f}")
            print(f"   Predictions: {len(result.prediction)} items")
            print(f"   Risk factors: {len(result.risk_factors)} identified")
            print(f"   Recommendations: {len(result.recommendations)} provided")
            return True
        else:
            print("❌ Analysis returned None")
            return False
            
    except Exception as e:
        print(f"❌ Quantitative agent test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_agent_performance():
    """Test agent performance with different data sizes"""
    print("\\n" + "="*50)
    print("AGENT PERFORMANCE TEST")
    print("="*50)
    
    import time
    
    try:
        # Test different data sizes
        data_sizes = [50, 100, 200]
        
        for size in data_sizes:
            print(f"\\nTesting with {size} days of data...")
            
            # Create data
            test_data = create_sample_data("PERF_TEST", size)
            
            # Test technical agent
            config_tech = {
                'lookback_period': min(50, size),
                'min_pattern_strength': 0.7,
                'signal_threshold': 0.6,
                'risk_tolerance': 0.02
            }
            
            tech_agent = EnhancedTechnicalAnalysisAgent(config_tech)
            await tech_agent.initialize()
            
            start_time = time.time()
            tech_result = await tech_agent.analyze(["PERF_TEST"], "1d", market_data=test_data)
            tech_time = time.time() - start_time
            
            print(f"   Technical analysis: {tech_time:.2f}s")
            
            # Test quantitative agent
            config_quant = {
                'enable_garch': False,
                'enable_neural_network': False,
                'monte_carlo_simulations': 50
            }
            
            quant_agent = EnhancedQuantitativeAgent(config_quant)
            await quant_agent.initialize()
            
            start_time = time.time()
            quant_result = await quant_agent.analyze(["PERF_TEST"], "1d", market_data=test_data)
            quant_time = time.time() - start_time
            
            print(f"   Quantitative analysis: {quant_time:.2f}s")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance test failed: {str(e)}")
        return False

async def main():
    """Run simple integration tests"""
    print("ENHANCED AI AGENTS - SIMPLE INTEGRATION TESTS")
    print("=" * 60)
    
    test_results = []
    
    # Run tests
    tests = [
        ("Enhanced Technical Agent", test_enhanced_technical_agent_simple),
        ("Enhanced Quantitative Agent", test_enhanced_quantitative_agent_simple),
        ("Performance Test", test_agent_performance)
    ]
    
    for test_name, test_func in tests:
        try:
            print(f"\\nRunning {test_name}...")
            result = await test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {str(e)}")
            test_results.append((test_name, False))
    
    # Print summary
    print("\\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:.<40} {status}")
        if result:
            passed += 1
    
    print(f"\\nTotal: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Enhanced agents are working correctly.")
    else:
        print("⚠️  Some tests failed. Review and fix issues.")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\\n❌ Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\\n❌ Test suite failed: {str(e)}")
        sys.exit(1)
