#!/usr/bin/env python3
"""
Dr. Apex Data Source Validation Script
Real API testing with zero tolerance for hallucination
"""

import os
import sys
import requests
import json
from datetime import datetime

def load_env_variables():
    """Load API keys from .env file"""
    env_vars = {}
    env_path = '.env'
    if os.path.exists(env_path):
        with open(env_path, 'r') as f:
            for line in f:
                if '=' in line and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
                    env_vars[key] = value
    return env_vars

def test_alpha_vantage():
    """Test Alpha Vantage API with real data"""
    print('\n📊 Testing Alpha Vantage API...')
    try:
        api_key = os.getenv('ALPHA_VANTAGE_API_KEY')
        if not api_key:
            print('❌ Alpha Vantage: No API key found')
            return False
            
        url = f'https://www.alphavantage.co/query?function=TIME_SERIES_DAILY&symbol=AAPL&apikey={api_key}'
        response = requests.get(url, timeout=15)
        data = response.json()
        
        if 'Time Series (Daily)' in data:
            ts_data = data['Time Series (Daily)']
            latest_date = max(ts_data.keys())
            latest_price = float(ts_data[latest_date]['4. close'])
            volume = int(ts_data[latest_date]['5. volume'])
            print(f'✅ Alpha Vantage: AAPL ${latest_price:.2f} ({latest_date})')
            print(f'   Volume: {volume:,} shares')
            print(f'   Data points: {len(ts_data)}')
            return True
        else:
            print(f'❌ Alpha Vantage Error: {data}')
            return False
            
    except Exception as e:
        print(f'❌ Alpha Vantage Exception: {e}')
        return False

def test_fred_api():
    """Test FRED Economic Data API"""
    print('\n📈 Testing FRED Economic API...')
    try:
        api_key = os.getenv('FRED_API_KEY')
        if not api_key:
            print('❌ FRED: No API key found')
            return False
            
        url = f'https://api.stlouisfed.org/fred/series/observations?series_id=GDP&api_key={api_key}&file_type=json&limit=5&sort_order=desc'
        response = requests.get(url, timeout=15)
        data = response.json()
        
        if 'observations' in data:
            obs = data['observations'][0]
            gdp_value = obs['value']
            gdp_date = obs['date']
            print(f'✅ FRED: GDP ${gdp_value}T ({gdp_date})')
            return True
        else:
            print(f'❌ FRED Error: {data}')
            return False
            
    except Exception as e:
        print(f'❌ FRED Exception: {e}')
        return False

def test_polygon_api():
    """Test Polygon API"""
    print('\n📊 Testing Polygon API...')
    try:
        api_key = os.getenv('POLYGON_API_KEY')
        if not api_key:
            print('❌ Polygon: No API key found')
            return False
            
        url = f'https://api.polygon.io/v2/aggs/ticker/AAPL/prev?adjusted=true&apikey={api_key}'
        response = requests.get(url, timeout=15)
        data = response.json()
        
        if data.get('status') == 'OK' and 'results' in data:
            result = data['results'][0]
            close_price = result['c']
            volume = result['v']
            print(f'✅ Polygon: AAPL ${close_price:.2f} (Volume: {volume:,})')
            return True
        else:
            print(f'❌ Polygon Error: {data}')
            return False
            
    except Exception as e:
        print(f'❌ Polygon Exception: {e}')
        return False

def test_yfinance_fallback():
    """Test yfinance as fallback data source"""
    print('\n📊 Testing yfinance fallback...')
    try:
        import yfinance as yf
        ticker = yf.Ticker('AAPL')
        hist = ticker.history(period='5d')
        
        if not hist.empty:
            latest_price = hist['Close'].iloc[-1]
            latest_volume = hist['Volume'].iloc[-1]
            print(f'✅ yfinance: AAPL ${latest_price:.2f}')
            print(f'   Volume: {latest_volume:,.0f} shares')
            print(f'   Data points: {len(hist)}')
            return True
        else:
            print('❌ yfinance: No data returned')
            return False
            
    except Exception as e:
        print(f'❌ yfinance Exception: {e}')
        return False

def main():
    """Main validation function"""
    print('🔍 Dr. Apex Data Source Validation')
    print('=' * 50)
    
    # Load environment variables
    env_vars = load_env_variables()
    print(f'🔑 Loaded {len(env_vars)} environment variables')
    
    # Test each data source
    results = {
        'alpha_vantage': test_alpha_vantage(),
        'fred': test_fred_api(),
        'polygon': test_polygon_api(),
        'yfinance': test_yfinance_fallback()
    }
    
    # Summary
    print('\n🎯 Data Source Validation Summary')
    print('=' * 40)
    
    working_sources = 0
    for source, status in results.items():
        status_icon = '✅' if status else '❌'
        print(f'{status_icon} {source.upper()}: {"OPERATIONAL" if status else "FAILED"}')
        if status:
            working_sources += 1
    
    print(f'\n📊 Working Sources: {working_sources}/{len(results)}')
    
    if working_sources >= 2:
        print('✅ SUFFICIENT DATA SOURCES FOR PRODUCTION')
    else:
        print('❌ INSUFFICIENT DATA SOURCES - SYSTEM NOT READY')
    
    return working_sources >= 2

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
