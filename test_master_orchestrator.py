#!/usr/bin/env python3
"""
Dr. <PERSON><PERSON> Master Orchestrator Integration Test
Real multi-agent system validation with production data
"""

import os
import sys
import asyncio
import json
from datetime import datetime

# Load environment variables
def load_env():
    with open('.env', 'r') as f:
        for line in f:
            if '=' in line and not line.startswith('#'):
                key, value = line.strip().split('=', 1)
                os.environ[key] = value

load_env()
sys.path.insert(0, '/Users/<USER>/crypto')

async def test_quantitative_agent_only():
    """Test quantitative agent in isolation"""
    print('🧮 Testing Quantitative Agent (Isolated)')
    print('=' * 45)
    
    try:
        from src.agents.quantitative import QuantitativeAgent
        
        config = {
            'lookback_period': 100,
            'min_observations': 20,
            'confidence_level': 0.95
        }
        
        agent = QuantitativeAgent(config)
        await agent.initialize()
        
        # Test with real symbols
        symbols = ['AAPL', 'MSFT']
        result = await agent.analyze(symbols, '1d')
        
        print(f'✅ Quantitative Agent Analysis Complete')
        print(f'   Confidence: {result.confidence:.3f}')
        print(f'   Data Quality: {result.data_quality:.3f}')
        print(f'   Recommendations: {len(result.recommendations)}')
        print(f'   Risk Factors: {len(result.risk_factors)}')
        
        # Validate predictions structure
        predictions = result.predictions
        components = ['factor_analysis', 'risk_model', 'strategy_signals', 'ml_predictions']
        
        print('\n🔬 Prediction Components:')
        for component in components:
            if component in predictions:
                data = predictions[component]
                if isinstance(data, dict):
                    print(f'   ✅ {component}: {len(data)} items')
                else:
                    print(f'   ✅ {component}: Available')
            else:
                print(f'   ❌ {component}: Missing')
        
        return result
        
    except Exception as e:
        print(f'❌ Quantitative Agent Error: {e}')
        import traceback
        traceback.print_exc()
        return None

async def test_stock_forecasting_engine():
    """Test the stock forecasting engine directly"""
    print('\n📈 Testing Stock Forecasting Engine')
    print('=' * 40)
    
    try:
        from src.orchestration.master_orchestrator import StockForecastingEngine
        
        engine = StockForecastingEngine()
        
        # Test single stock forecast
        forecast = engine.generate_forecast('AAPL', days=5)
        
        if 'error' in forecast:
            print(f'❌ Forecast Error: {forecast["error"]}')
            return None
        
        print(f'✅ Stock Forecast Generated')
        print(f'   Symbol: {forecast["symbol"]}')
        print(f'   Current Price: ${forecast["current_price"]:.2f}')
        print(f'   Target Price: ${forecast["target_price"]:.2f}')
        print(f'   Expected Return: {forecast["expected_return"]:.3f}')
        print(f'   Confidence: {forecast["confidence"]:.3f}')
        
        # Validate signal components
        signals = forecast.get('signals', {})
        print(f'\n🔬 Signal Components:')
        for signal_name, value in signals.items():
            print(f'   📊 {signal_name}: {value:.4f}')
        
        return forecast
        
    except Exception as e:
        print(f'❌ Stock Forecasting Engine Error: {e}')
        import traceback
        traceback.print_exc()
        return None

async def test_bayesian_synthesizer():
    """Test Bayesian synthesis with mock agent results"""
    print('\n🧠 Testing Bayesian Synthesizer')
    print('=' * 35)
    
    try:
        from src.orchestration.master_orchestrator import BayesianSynthesizer
        from src.agents.base import AgentResult
        
        synthesizer = BayesianSynthesizer()
        
        # Create mock agent results
        mock_results = {}
        
        # Mock quantitative result
        mock_results['quantitative'] = AgentResult(
            agent_name='quantitative',
            analysis_type='quantitative',
            timestamp=datetime.now(),
            symbols=['AAPL'],
            confidence=0.75,
            predictions={
                'price_forecasts': {
                    'AAPL': {'expected_return': 0.05}
                }
            },
            recommendations=['Buy AAPL'],
            risk_factors=['Market volatility'],
            data_quality=0.9
        )
        
        # Mock technical result
        mock_results['technical_analysis'] = AgentResult(
            agent_name='technical_analysis',
            analysis_type='technical',
            timestamp=datetime.now(),
            symbols=['AAPL'],
            confidence=0.65,
            predictions={
                'trading_signals': {
                    'AAPL': {
                        'combined_signal': {
                            'direction': 'buy',
                            'strength': 0.7
                        }
                    }
                }
            },
            recommendations=['Technical buy signal'],
            risk_factors=['Overbought conditions'],
            data_quality=0.8
        )
        
        # Test synthesis
        synthesis_result = synthesizer.synthesize_forecasts(mock_results)
        
        print(f'✅ Bayesian Synthesis Complete')
        
        consensus = synthesis_result.get('consensus_forecasts', {})
        uncertainty = synthesis_result.get('uncertainty_estimates', {})
        weights = synthesis_result.get('agent_weights', {})
        
        print(f'\n🔬 Synthesis Results:')
        print(f'   📊 Consensus forecasts: {len(consensus)} symbols')
        print(f'   📊 Uncertainty estimates: {len(uncertainty)} symbols')
        print(f'   📊 Agent weights: {len(weights)} agents')
        
        # Show agent weights
        print(f'\n🎯 Agent Weights:')
        for agent, weight in weights.items():
            print(f'   {agent}: {weight:.3f}')
        
        return synthesis_result
        
    except Exception as e:
        print(f'❌ Bayesian Synthesizer Error: {e}')
        import traceback
        traceback.print_exc()
        return None

async def test_black_swan_detector():
    """Test black swan risk detection"""
    print('\n🦢 Testing Black Swan Detector')
    print('=' * 32)
    
    try:
        from src.orchestration.master_orchestrator import BlackSwanDetector
        from src.agents.base import AgentResult
        
        detector = BlackSwanDetector()
        
        # Create mock agent results with risk factors
        mock_results = {
            'quantitative': AgentResult(
                agent_name='quantitative',
                analysis_type='quantitative',
                timestamp=datetime.now(),
                symbols=['AAPL'],
                confidence=0.4,  # Low confidence indicates uncertainty
                predictions={},
                recommendations=[],
                risk_factors=['High volatility detected', 'Correlation breakdown'],
                data_quality=0.7
            )
        }
        
        # Test black swan detection
        risks = detector.detect_black_swan_risks(mock_results)
        
        print(f'✅ Black Swan Detection Complete')
        print(f'   📊 Risks detected: {len(risks)}')
        
        for i, risk in enumerate(risks[:3]):  # Show top 3 risks
            print(f'\n🚨 Risk {i+1}:')
            print(f'   Type: {risk.get("type", "unknown")}')
            print(f'   Description: {risk.get("description", "N/A")}')
            print(f'   Probability: {risk.get("probability", 0):.3f}')
            print(f'   Impact: {risk.get("impact", 0):.3f}')
            if 'risk_score' in risk:
                print(f'   Risk Score: {risk["risk_score"]:.3f}')
        
        return risks
        
    except Exception as e:
        print(f'❌ Black Swan Detector Error: {e}')
        import traceback
        traceback.print_exc()
        return None

async def main():
    """Main orchestrator integration test"""
    print('🎯 Dr. Apex Master Orchestrator Integration Test')
    print('=' * 55)
    
    results = {}
    
    # Test individual components
    print('\n📋 Testing Individual Components...')
    results['quantitative_agent'] = await test_quantitative_agent_only()
    results['stock_forecasting'] = await test_stock_forecasting_engine()
    results['bayesian_synthesis'] = await test_bayesian_synthesizer()
    results['black_swan_detection'] = await test_black_swan_detector()
    
    # Summary
    print('\n🎯 Master Orchestrator Integration Summary')
    print('=' * 45)
    
    working_components = 0
    for component, result in results.items():
        status = '✅' if result is not None else '❌'
        status_text = 'OPERATIONAL' if result is not None else 'FAILED'
        print(f'{status} {component.upper()}: {status_text}')
        if result is not None:
            working_components += 1
    
    print(f'\n📊 Working Components: {working_components}/{len(results)}')
    
    if working_components >= 3:
        print('✅ MASTER ORCHESTRATOR: SUFFICIENT FOR PRODUCTION')
        print('🎯 Recommendation: Deploy with working components only')
    else:
        print('❌ MASTER ORCHESTRATOR: REQUIRES FIXES')
        print('🔧 Recommendation: Fix broken components before deployment')
    
    return working_components >= 3

if __name__ == '__main__':
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
