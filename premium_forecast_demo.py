#!/usr/bin/env python3
"""
Premium Stock Forecasting Demo
Uses your actual API keys for live data and analysis
"""

import asyncio
import sys
import os
from datetime import datetime

# Add project root and load environment
sys.path.append('/Users/<USER>/crypto')
from dotenv import load_dotenv
load_dotenv('/Users/<USER>/crypto/.env')

from src.orchestration.master_orchestrator import MasterOrchestrator
from src.core.system import APICredentials

class PremiumDataSource:
    """Premium data source using your API keys"""
    
    def __init__(self):
        self.creds = APICredentials()
        
    async def get_stock_data(self, symbol: str):
        """Get stock data using your premium APIs"""
        try:
            # Try Alpha Vantage first (you have a working key)
            if self.creds.alpha_vantage_api_key:
                data = await self._get_alpha_vantage_data(symbol)
                if data is not None:
                    return data
            
            # Fallback to yfinance
            return await self._get_yfinance_data(symbol)
            
        except Exception as e:
            print(f"Error fetching data for {symbol}: {e}")
            return None
    
    async def _get_alpha_vantage_data(self, symbol: str):
        """Get data from Alpha Vantage API"""
        try:
            import aiohttp
            import pandas as pd
            
            # Get daily data
            url = f"https://www.alphavantage.co/query"
            params = {
                'function': 'TIME_SERIES_DAILY',
                'symbol': symbol,
                'apikey': self.creds.alpha_vantage_api_key,
                'outputsize': 'compact'  # Last 100 days
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        # Check for API limit
                        if "Note" in data:
                            print(f"⚠️  Alpha Vantage rate limited: {data['Note']}")
                            return None
                        
                        # Parse time series data
                        if "Time Series (Daily)" in data:
                            time_series = data["Time Series (Daily)"]
                            
                            # Convert to DataFrame
                            df_data = []
                            for date_str, values in time_series.items():
                                df_data.append({
                                    'date': pd.to_datetime(date_str),
                                    'open': float(values['1. open']),
                                    'high': float(values['2. high']),
                                    'low': float(values['3. low']),
                                    'close': float(values['4. close']),
                                    'volume': int(values['5. volume'])
                                })
                            
                            df = pd.DataFrame(df_data)
                            df.set_index('date', inplace=True)
                            df.sort_index(inplace=True)
                            
                            print(f"✅ Alpha Vantage: Fetched {len(df)} days of data for {symbol}")
                            return df
                        
                        else:
                            print(f"⚠️  Alpha Vantage: Unexpected response format for {symbol}")
                            return None
                    else:
                        print(f"❌ Alpha Vantage: HTTP {response.status}")
                        return None
                        
        except Exception as e:
            print(f"❌ Alpha Vantage error: {e}")
            return None
    
    async def _get_yfinance_data(self, symbol: str):
        """Get data from Yahoo Finance as fallback"""
        try:
            import yfinance as yf
            
            ticker = yf.Ticker(symbol)
            data = ticker.history(period="6mo", interval="1d")
            
            if not data.empty:
                # Standardize column names
                data.columns = [col.lower() for col in data.columns]
                print(f"✅ Yahoo Finance: Fetched {len(data)} days of data for {symbol}")
                return data
            else:
                print(f"⚠️  Yahoo Finance: No data for {symbol}")
                return None
                
        except Exception as e:
            print(f"❌ Yahoo Finance error: {e}")
            return None

async def run_premium_demo():
    """Run demo with your premium API keys"""
    print("🚀 Premium Stock Forecasting System")
    print("Using YOUR actual API keys for live data!")
    print("=" * 60)
    
    # Initialize systems
    orchestrator = MasterOrchestrator()
    data_source = PremiumDataSource()
    
    # Test symbols
    symbols = ["AAPL", "MSFT", "GOOGL", "TSLA"]
    
    print(f"\n📊 Analyzing {len(symbols)} stocks with premium data...")
    
    results = {}
    
    for symbol in symbols:
        print(f"\n🔍 Analyzing {symbol}...")
        
        # Get premium data
        market_data = await data_source.get_stock_data(symbol)
        
        if market_data is not None:
            # Apply noise filtering
            noise_result = orchestrator.noise_filter.filter_market_noise(market_data, symbol)
            
            # Generate forecast
            forecast = orchestrator.stock_analyzer.generate_stock_forecast(
                symbol, noise_result['filtered_data']
            )
            
            results[symbol] = {
                'forecast': forecast,
                'data_quality': noise_result['data_quality'],
                'data_source': 'Alpha Vantage' if hasattr(market_data, 'source') else 'Yahoo Finance'
            }
            
            # Display results
            consensus = forecast['consensus_forecast']
            print(f"   📈 Expected return: {consensus['expected_return']:.2%}")
            print(f"   🎯 Confidence: {consensus['confidence']:.1%}")
            print(f"   📊 Data quality: {noise_result['data_quality']:.1%}")
            print(f"   🔌 Source: {results[symbol]['data_source']}")
            
        else:
            print(f"   ❌ Failed to get data for {symbol}")
            results[symbol] = None
    
    # Portfolio analysis
    print(f"\n📋 Portfolio Analysis")
    print("=" * 30)
    
    valid_forecasts = {k: v for k, v in results.items() if v is not None}
    
    if valid_forecasts:
        avg_return = sum(v['forecast']['consensus_forecast']['expected_return'] 
                        for v in valid_forecasts.values()) / len(valid_forecasts)
        avg_confidence = sum(v['forecast']['consensus_forecast']['confidence'] 
                           for v in valid_forecasts.values()) / len(valid_forecasts)
        avg_quality = sum(v['data_quality'] for v in valid_forecasts.values()) / len(valid_forecasts)
        
        print(f"📊 Portfolio expected return: {avg_return:.2%}")
        print(f"🎯 Average confidence: {avg_confidence:.1%}")
        print(f"📈 Average data quality: {avg_quality:.1%}")
        
        # Recommendations
        print(f"\n💡 Recommendations:")
        for symbol, result in valid_forecasts.items():
            if result is not None:
                expected_return = result['forecast']['consensus_forecast']['expected_return']
                confidence = result['forecast']['consensus_forecast']['confidence']
                
                if expected_return > 0.02 and confidence > 0.7:
                    print(f"   🟢 {symbol}: STRONG BUY (Return: {expected_return:.1%}, Confidence: {confidence:.1%})")
                elif expected_return > 0.01 and confidence > 0.6:
                    print(f"   🔵 {symbol}: BUY (Return: {expected_return:.1%}, Confidence: {confidence:.1%})")
                elif expected_return < -0.02 and confidence > 0.7:
                    print(f"   🔴 {symbol}: STRONG SELL (Return: {expected_return:.1%}, Confidence: {confidence:.1%})")
                elif expected_return < -0.01 and confidence > 0.6:
                    print(f"   🟠 {symbol}: SELL (Return: {expected_return:.1%}, Confidence: {confidence:.1%})")
                else:
                    print(f"   ⚪ {symbol}: HOLD (Return: {expected_return:.1%}, Confidence: {confidence:.1%})")
    
    print(f"\n🎉 Premium analysis complete!")
    print(f"📊 Analyzed {len(valid_forecasts)}/{len(symbols)} stocks successfully")
    print(f"🔑 Using your premium API keys for maximum data quality")
    
    return results

if __name__ == "__main__":
    # Run the premium demo
    results = asyncio.run(run_premium_demo())
