#!/usr/bin/env python3
"""
Step-by-Step Calculation Demo for AAPL
Shows the exact mathematical foundation of our forecasting system
"""

import yfinance as yf
import pandas as pd
import numpy as np
from scipy import stats
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def print_section(title, data=None):
    """Print formatted section header"""
    print("\n" + "="*60)
    print(f"  {title}")
    print("="*60)
    if data is not None:
        print(data)

def fetch_aapl_data():
    """Fetch AAPL data for demonstration"""
    print_section("FETCHING REAL AAPL DATA")
    
    ticker = yf.Ticker("AAPL")
    data = ticker.history(period="6mo", interval="1d")
    
    # Standardize column names
    data.columns = [col.lower() for col in data.columns]
    
    print(f"Data period: {data.index[0].date()} to {data.index[-1].date()}")
    print(f"Total trading days: {len(data)}")
    print(f"Current price: ${data['close'].iloc[-1]:.2f}")
    
    # Show recent data
    print("\nLast 5 trading days:")
    recent = data[['close', 'volume']].tail(5)
    for date, row in recent.iterrows():
        print(f"  {date.date()}: Close ${row['close']:.2f}, Volume {row['volume']:,}")
    
    return data

def demonstrate_momentum_calculation(data):
    """Step-by-step momentum calculation"""
    print_section("MOMENTUM CALCULATION BREAKDOWN")
    
    current_price = data['close'].iloc[-1]
    print(f"Current AAPL price: ${current_price:.2f}")
    
    # Calculate returns
    returns = data['close'].pct_change()
    print(f"\nDaily returns calculated: {len(returns)} observations")
    print("Last 5 daily returns:")
    for i, ret in enumerate(returns.tail(5)):
        date = data.index[-5+i].date()
        print(f"  {date}: {ret:.4f} ({ret*100:.2f}%)")
    
    # 1. Short-term momentum (5-day average return)
    short_momentum = returns.rolling(window=5).mean().iloc[-1]
    print(f"\n1. SHORT-TERM MOMENTUM (5-day average):")
    print(f"   Formula: mean(last 5 daily returns)")
    print(f"   Calculation: {short_momentum:.6f}")
    print(f"   Percentage: {short_momentum*100:.3f}%")
    
    # 2. Medium-term momentum (20-day average return)
    medium_momentum = returns.rolling(window=20).mean().iloc[-1]
    print(f"\n2. MEDIUM-TERM MOMENTUM (20-day average):")
    print(f"   Formula: mean(last 20 daily returns)")
    print(f"   Calculation: {medium_momentum:.6f}")
    print(f"   Percentage: {medium_momentum*100:.3f}%")
    
    # 3. RSI calculation
    print(f"\n3. RSI (Relative Strength Index) CALCULATION:")
    delta = data['close'].diff()
    gains = delta.where(delta > 0, 0)
    losses = -delta.where(delta < 0, 0)
    
    avg_gain = gains.rolling(window=14).mean().iloc[-1]
    avg_loss = losses.rolling(window=14).mean().iloc[-1]
    
    print(f"   Average gain (14 days): ${avg_gain:.4f}")
    print(f"   Average loss (14 days): ${avg_loss:.4f}")
    
    if avg_loss != 0:
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        print(f"   RS (Relative Strength): {rs:.4f}")
        print(f"   RSI: {rsi:.2f}")
        
        # Convert RSI to signal
        rsi_signal = (50 - rsi) / 100
        print(f"   RSI Signal: {rsi_signal:.4f} (50-RSI)/100")
    else:
        rsi = 50
        rsi_signal = 0
        print(f"   RSI: {rsi:.2f} (no losses, neutral)")
        print(f"   RSI Signal: {rsi_signal:.4f}")
    
    # 4. Combine momentum signals
    print(f"\n4. MOMENTUM SIGNAL COMBINATION:")
    print(f"   Weights: Short(40%) + Medium(40%) + RSI(20%)")
    
    momentum_forecast = (
        short_momentum * 0.4 +
        medium_momentum * 0.4 +
        rsi_signal * 0.2
    )
    
    print(f"   Calculation:")
    print(f"     ({short_momentum:.6f} × 0.4) + ({medium_momentum:.6f} × 0.4) + ({rsi_signal:.6f} × 0.2)")
    print(f"     = {short_momentum*0.4:.6f} + {medium_momentum*0.4:.6f} + {rsi_signal*0.2:.6f}")
    print(f"     = {momentum_forecast:.6f}")
    print(f"   Final momentum forecast: {momentum_forecast*100:.3f}%")
    
    return momentum_forecast

def demonstrate_reversion_calculation(data):
    """Step-by-step mean reversion calculation"""
    print_section("MEAN REVERSION CALCULATION BREAKDOWN")
    
    current_price = data['close'].iloc[-1]
    prices = data['close']
    
    # Moving averages
    ma_20 = prices.rolling(window=20).mean().iloc[-1]
    ma_50 = prices.rolling(window=50).mean().iloc[-1]
    
    print(f"Current price: ${current_price:.2f}")
    print(f"20-day MA: ${ma_20:.2f}")
    print(f"50-day MA: ${ma_50:.2f}")
    
    # 1. Short-term reversion
    short_reversion = (ma_20 - current_price) / current_price
    print(f"\n1. SHORT-TERM REVERSION (vs 20-day MA):")
    print(f"   Formula: (MA20 - Current) / Current")
    print(f"   Calculation: (${ma_20:.2f} - ${current_price:.2f}) / ${current_price:.2f}")
    print(f"   Result: {short_reversion:.6f} ({short_reversion*100:.3f}%)")
    
    # 2. Long-term reversion
    long_reversion = (ma_50 - current_price) / current_price
    print(f"\n2. LONG-TERM REVERSION (vs 50-day MA):")
    print(f"   Formula: (MA50 - Current) / Current")
    print(f"   Calculation: (${ma_50:.2f} - ${current_price:.2f}) / ${current_price:.2f}")
    print(f"   Result: {long_reversion:.6f} ({long_reversion*100:.3f}%)")
    
    # 3. Bollinger Bands
    print(f"\n3. BOLLINGER BANDS CALCULATION:")
    sma_20 = prices.rolling(window=20).mean().iloc[-1]
    std_20 = prices.rolling(window=20).std().iloc[-1]
    bb_upper = sma_20 + (std_20 * 2)
    bb_lower = sma_20 - (std_20 * 2)
    
    print(f"   20-day SMA: ${sma_20:.2f}")
    print(f"   20-day STD: ${std_20:.2f}")
    print(f"   Upper band: ${bb_upper:.2f} (SMA + 2×STD)")
    print(f"   Lower band: ${bb_lower:.2f} (SMA - 2×STD)")
    
    bb_position = (current_price - bb_lower) / (bb_upper - bb_lower)
    bb_signal = (0.5 - bb_position) * 0.1
    
    print(f"   BB Position: {bb_position:.4f} (0=lower, 1=upper)")
    print(f"   BB Signal: {bb_signal:.6f} ((0.5 - position) × 0.1)")
    
    # 4. Combine reversion signals
    print(f"\n4. REVERSION SIGNAL COMBINATION:")
    print(f"   Weights: Short(40%) + Long(40%) + Bollinger(20%)")
    
    reversion_forecast = (
        short_reversion * 0.4 +
        long_reversion * 0.4 +
        bb_signal * 0.2
    )
    
    print(f"   Calculation:")
    print(f"     ({short_reversion:.6f} × 0.4) + ({long_reversion:.6f} × 0.4) + ({bb_signal:.6f} × 0.2)")
    print(f"     = {short_reversion*0.4:.6f} + {long_reversion*0.4:.6f} + {bb_signal*0.2:.6f}")
    print(f"     = {reversion_forecast:.6f}")
    print(f"   Final reversion forecast: {reversion_forecast*100:.3f}%")
    
    return reversion_forecast

def demonstrate_volatility_adjustment(data):
    """Step-by-step volatility adjustment calculation"""
    print_section("VOLATILITY ADJUSTMENT CALCULATION")
    
    returns = data['close'].pct_change()
    volatility = returns.rolling(window=20).std().iloc[-1]
    
    print(f"20-day return volatility: {volatility:.6f} ({volatility*100:.3f}%)")
    print(f"Annualized volatility: {volatility*np.sqrt(252):.3f} ({volatility*np.sqrt(252)*100:.1f}%)")
    
    # Volatility adjustment formula
    vol_adjustment = 1.0 / (1.0 + volatility * 10)
    vol_adjustment_clipped = np.clip(vol_adjustment, 0.3, 1.0)
    
    print(f"\nVolatility adjustment calculation:")
    print(f"   Formula: 1.0 / (1.0 + volatility × 10)")
    print(f"   Raw calculation: 1.0 / (1.0 + {volatility:.6f} × 10)")
    print(f"   Raw result: 1.0 / {1.0 + volatility*10:.6f} = {vol_adjustment:.6f}")
    print(f"   Clipped (0.3-1.0): {vol_adjustment_clipped:.6f}")
    print(f"   Interpretation: {'High volatility reduces signal strength' if vol_adjustment_clipped < 0.7 else 'Moderate volatility impact'}")
    
    return vol_adjustment_clipped

def demonstrate_volume_confirmation(data):
    """Step-by-step volume confirmation calculation"""
    print_section("VOLUME CONFIRMATION CALCULATION")
    
    if 'volume' not in data.columns:
        print("No volume data available")
        return 0.5
    
    # Average volume calculation
    lookback = 20
    avg_volume = data['volume'].tail(lookback).mean()
    recent_volume = data['volume'].tail(5).mean()
    
    print(f"Volume analysis (lookback: {lookback} days, recent: 5 days):")
    print(f"   Average volume ({lookback} days): {avg_volume:,.0f}")
    print(f"   Recent volume (5 days): {recent_volume:,.0f}")
    
    if avg_volume == 0:
        print("   No volume data, using neutral factor: 0.5")
        return 0.5
    
    volume_ratio = recent_volume / avg_volume
    print(f"   Volume ratio: {recent_volume:,.0f} / {avg_volume:,.0f} = {volume_ratio:.3f}")
    
    # Convert to confidence factor
    volume_factor_raw = min(volume_ratio, 2.0) / 2.0
    volume_factor = np.clip(volume_factor_raw, 0.2, 1.0)
    
    print(f"   Volume factor calculation:")
    print(f"     Raw factor: min({volume_ratio:.3f}, 2.0) / 2.0 = {volume_factor_raw:.3f}")
    print(f"     Clipped (0.2-1.0): {volume_factor:.3f}")
    
    interpretation = ""
    if volume_factor > 0.8:
        interpretation = "Strong volume confirmation"
    elif volume_factor > 0.6:
        interpretation = "Moderate volume confirmation"
    else:
        interpretation = "Weak volume confirmation"
    
    print(f"   Interpretation: {interpretation}")
    
    return volume_factor

def demonstrate_trend_strength(data):
    """Step-by-step trend strength calculation using R-squared"""
    print_section("TREND STRENGTH CALCULATION (R-SQUARED)")
    
    # Use 20-day lookback for trend analysis
    lookback = 20
    prices = data['close'].tail(lookback).values
    
    print(f"Trend analysis using last {lookback} trading days:")
    print(f"Price range: ${prices[0]:.2f} to ${prices[-1]:.2f}")
    
    # Linear regression
    x = np.arange(len(prices))
    slope, intercept, r_value, p_value, std_err = stats.linregress(x, prices)
    
    r_squared = r_value ** 2
    
    print(f"\nLinear regression results:")
    print(f"   Slope: ${slope:.4f} per day")
    print(f"   Intercept: ${intercept:.2f}")
    print(f"   R-value: {r_value:.4f}")
    print(f"   R-squared: {r_squared:.4f}")
    print(f"   P-value: {p_value:.6f}")
    
    # Convert R-squared to trend strength
    if r_squared > 0.8:
        trend_strength = 0.9 + 0.1 * (r_squared - 0.8) / 0.2
        interpretation = "Very strong trend"
    elif r_squared > 0.5:
        trend_strength = 0.7 + 0.2 * (r_squared - 0.5) / 0.3
        interpretation = "Moderate trend"
    else:
        trend_strength = 0.5 + 0.2 * r_squared / 0.5
        interpretation = "Weak/sideways trend"
    
    trend_strength_clipped = np.clip(trend_strength, 0.3, 1.0)
    
    print(f"\nTrend strength conversion:")
    print(f"   R-squared: {r_squared:.4f}")
    print(f"   Trend strength: {trend_strength:.4f}")
    print(f"   Clipped (0.3-1.0): {trend_strength_clipped:.4f}")
    print(f"   Interpretation: {interpretation}")
    
    return trend_strength_clipped

def demonstrate_signal_integration(momentum, reversion, vol_adj, volume_conf, trend_strength):
    """Step-by-step signal integration and confidence calculation"""
    print_section("SIGNAL INTEGRATION & CONFIDENCE CALCULATION")
    
    # Model weights
    weights = {
        'technical_momentum': 0.25,
        'mean_reversion': 0.25,
        'fundamental_scoring': 0.30,  # Will be 0 for this demo
        'volatility_modeling': 0.20
    }
    
    fundamental_signal = 0.0  # No fundamental data in this demo
    
    print(f"Signal combination with model weights:")
    print(f"   Momentum signal: {momentum:.6f} (weight: {weights['technical_momentum']})")
    print(f"   Reversion signal: {reversion:.6f} (weight: {weights['mean_reversion']})")
    print(f"   Fundamental signal: {fundamental_signal:.6f} (weight: {weights['fundamental_scoring']})")
    print(f"   Volatility adjustment: {vol_adj:.6f} (weight: {weights['volatility_modeling']})")
    
    # Weighted forecast
    weighted_forecast = (
        momentum * weights['technical_momentum'] +
        reversion * weights['mean_reversion'] +
        fundamental_signal * weights['fundamental_scoring']
    )
    
    print(f"\nWeighted forecast calculation:")
    print(f"   ({momentum:.6f} × {weights['technical_momentum']}) + ({reversion:.6f} × {weights['mean_reversion']}) + ({fundamental_signal:.6f} × {weights['fundamental_scoring']})")
    print(f"   = {momentum * weights['technical_momentum']:.6f} + {reversion * weights['mean_reversion']:.6f} + {fundamental_signal * weights['fundamental_scoring']:.6f}")
    print(f"   = {weighted_forecast:.6f}")
    
    # Apply volatility adjustment
    adjusted_forecast = weighted_forecast * vol_adj
    print(f"\nVolatility-adjusted forecast:")
    print(f"   {weighted_forecast:.6f} × {vol_adj:.6f} = {adjusted_forecast:.6f}")
    print(f"   Percentage return: {adjusted_forecast*100:.3f}%")
    
    # Enhanced confidence calculation
    print(f"\nCONFIDENCE CALCULATION:")
    
    # 1. Signal normalization using tanh
    raw_signals = [momentum, reversion, fundamental_signal]
    normalized_signals = [np.tanh(signal * 10) for signal in raw_signals]
    
    print(f"   Raw signals: {[f'{s:.6f}' for s in raw_signals]}")
    print(f"   Normalized (tanh): {[f'{s:.6f}' for s in normalized_signals]}")
    
    # 2. Directional agreement
    positive_signals = sum(1 for s in normalized_signals if s > 0.1)
    negative_signals = sum(1 for s in normalized_signals if s < -0.1)
    
    directional_bonus = 0.15 if (positive_signals >= 2 or negative_signals >= 2) else 0.0
    print(f"   Positive signals: {positive_signals}, Negative: {negative_signals}")
    print(f"   Directional bonus: {directional_bonus:.3f}")
    
    # 3. Signal strength
    avg_strength = np.mean(np.abs(normalized_signals))
    strength_factor = min(avg_strength * 2, 1.0)
    print(f"   Average signal strength: {avg_strength:.4f}")
    print(f"   Strength factor: {strength_factor:.4f}")
    
    # 4. Signal agreement
    signal_agreement = 1.0 - np.std(normalized_signals) / (np.mean(np.abs(normalized_signals)) + 0.1)
    print(f"   Signal agreement: {signal_agreement:.4f}")
    
    # 5. Base confidence
    base_confidence = (
        signal_agreement * 0.5 +
        strength_factor * 0.3 +
        directional_bonus
    )
    print(f"   Base confidence: ({signal_agreement:.4f} × 0.5) + ({strength_factor:.4f} × 0.3) + {directional_bonus:.3f}")
    print(f"   Base confidence: {base_confidence:.4f}")
    
    # 6. Apply volume and trend adjustments
    volume_weighted_conf = base_confidence * (0.7 + 0.3 * volume_conf)
    final_confidence = volume_weighted_conf * (0.8 + 0.2 * trend_strength)
    final_confidence_clipped = np.clip(final_confidence, 0.35, 0.90)
    
    print(f"   Volume adjustment: {base_confidence:.4f} × (0.7 + 0.3 × {volume_conf:.3f}) = {volume_weighted_conf:.4f}")
    print(f"   Trend adjustment: {volume_weighted_conf:.4f} × (0.8 + 0.2 × {trend_strength:.3f}) = {final_confidence:.4f}")
    print(f"   Final confidence (clipped): {final_confidence_clipped:.4f} ({final_confidence_clipped*100:.1f}%)")
    
    return adjusted_forecast, final_confidence_clipped

def demonstrate_price_target_calculation(data, expected_return):
    """Calculate and show price target"""
    print_section("PRICE TARGET CALCULATION")
    
    current_price = data['close'].iloc[-1]
    target_price = current_price * (1 + expected_return)
    
    print(f"Current AAPL price: ${current_price:.2f}")
    print(f"Expected return: {expected_return:.6f} ({expected_return*100:.3f}%)")
    print(f"Price target calculation:")
    print(f"   Target = Current × (1 + Expected Return)")
    print(f"   Target = ${current_price:.2f} × (1 + {expected_return:.6f})")
    print(f"   Target = ${current_price:.2f} × {1 + expected_return:.6f}")
    print(f"   Target = ${target_price:.2f}")
    
    price_change = target_price - current_price
    print(f"\nPrice change: ${price_change:+.2f}")
    
    if expected_return > 0.01:
        outlook = "BULLISH"
    elif expected_return < -0.01:
        outlook = "BEARISH"
    else:
        outlook = "NEUTRAL"
    
    print(f"Market outlook: {outlook}")
    
    return target_price

def main():
    """Run the complete step-by-step demonstration"""
    print("STEP-BY-STEP AAPL FORECAST CALCULATION DEMONSTRATION")
    print("=" * 60)
    print("This demo shows the exact mathematical foundation of our forecasting system")
    print("using real AAPL market data.")
    
    try:
        # 1. Fetch real data
        data = fetch_aapl_data()
        
        # 2. Calculate each component step-by-step
        momentum = demonstrate_momentum_calculation(data)
        reversion = demonstrate_reversion_calculation(data)
        vol_adj = demonstrate_volatility_adjustment(data)
        volume_conf = demonstrate_volume_confirmation(data)
        trend_strength = demonstrate_trend_strength(data)
        
        # 3. Integrate signals and calculate confidence
        expected_return, confidence = demonstrate_signal_integration(
            momentum, reversion, vol_adj, volume_conf, trend_strength
        )
        
        # 4. Calculate price target
        target_price = demonstrate_price_target_calculation(data, expected_return)
        
        # 5. Final summary
        print_section("FINAL FORECAST SUMMARY")
        current_price = data['close'].iloc[-1]
        print(f"AAPL Forecast Results:")
        print(f"   Current Price: ${current_price:.2f}")
        print(f"   Target Price: ${target_price:.2f}")
        print(f"   Expected Return: {expected_return*100:+.3f}%")
        print(f"   Confidence: {confidence*100:.1f}%")
        print(f"   Signal Strength: {abs(expected_return) * confidence:.4f}")
        
        print(f"\nComponent Contributions:")
        print(f"   Momentum Signal: {momentum*100:+.3f}%")
        print(f"   Reversion Signal: {reversion*100:+.3f}%")
        print(f"   Volatility Adjustment: {vol_adj:.3f}")
        print(f"   Volume Confirmation: {volume_conf:.3f}")
        print(f"   Trend Strength: {trend_strength:.3f}")
        
        print(f"\nMathematical Foundation:")
        print(f"   • Momentum: 5-day & 20-day returns + RSI")
        print(f"   • Reversion: Moving averages + Bollinger Bands")
        print(f"   • Volatility: 20-day rolling standard deviation")
        print(f"   • Volume: Recent vs average volume ratio")
        print(f"   • Trend: R-squared of 20-day price regression")
        print(f"   • Confidence: Signal normalization + agreement + strength")
        
        print(f"\nThis is NOT financial advice. This system:")
        print(f"   ✓ Uses mathematical/statistical analysis")
        print(f"   ✓ Provides realistic confidence scores (35-90%)")
        print(f"   ✓ Shows all calculation steps transparently")
        print(f"   ✗ Cannot predict unpredictable market events")
        print(f"   ✗ Does not include fundamental analysis")
        print(f"   ✗ Is not a guarantee of future performance")
        
    except Exception as e:
        print(f"\nDemo failed with error: {e}")
        print("This might be due to network issues or data availability.")

if __name__ == "__main__":
    main()
