#!/usr/bin/env python3
import yfinance as yf
import pandas as pd
import numpy as np

# Get AAPL data to analyze current signals
ticker = yf.Ticker('AAPL')
data = ticker.history(period='6mo', interval='1d')

# Calculate current signals like the system does
returns = data['Close'].pct_change()
short_momentum = returns.rolling(window=5).mean().iloc[-1]
medium_momentum = returns.rolling(window=20).mean().iloc[-1]

# RSI calculation
def calc_rsi(prices, period=14):
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi.iloc[-1]

rsi = calc_rsi(data['Close'])
rsi_signal = (50 - rsi) / 100

# Calculate reversion signals
ma_20 = data['Close'].rolling(window=20).mean().iloc[-1]
ma_50 = data['Close'].rolling(window=50).mean().iloc[-1]
current_price = data['Close'].iloc[-1]

short_reversion = (ma_20 - current_price) / current_price
long_reversion = (ma_50 - current_price) / current_price

print(f'AAPL Analysis:')
print(f'Short momentum: {short_momentum:.4f}')
print(f'Medium momentum: {medium_momentum:.4f}')
print(f'RSI: {rsi:.1f}, RSI signal: {rsi_signal:.4f}')
print(f'Short reversion: {short_reversion:.4f}')
print(f'Long reversion: {long_reversion:.4f}')
print(f'Current price: ${current_price:.2f}')

# Check signal agreement
signals = [short_momentum, medium_momentum, rsi_signal]
signal_std = np.std(signals)
signal_mean = np.mean(np.abs(signals))
agreement = 1.0 - signal_std / (signal_mean + 0.01)
print(f'Signal std: {signal_std:.4f}')
print(f'Signal mean: {signal_mean:.4f}')
print(f'Agreement: {agreement:.3f}')

# Check volatility
volatility = returns.rolling(window=20).std().iloc[-1]
print(f'20-day volatility: {volatility:.4f}')

# Check volume trends
volume_mean = data['Volume'].rolling(window=20).mean().iloc[-1]
recent_volume = data['Volume'].iloc[-5:].mean()
volume_ratio = recent_volume / volume_mean
print(f'Volume ratio (recent/avg): {volume_ratio:.2f}')
